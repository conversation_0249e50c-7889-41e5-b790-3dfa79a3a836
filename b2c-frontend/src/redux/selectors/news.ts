/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import type { RootState } from '../store';

export const selectNews = (state: RootState) => state.news.news;
export const selectNewsTopics = (state: RootState) => state.news.topics;
export const selectNewsFilters = (state: RootState) => state.news.filters;
export const selectNewsPagination = (state: RootState) => state.news.pagination;
export const selectNewsLoading = (state: RootState) => state.news.loading;
export const selectNewsError = (state: RootState) => state.news.error;
