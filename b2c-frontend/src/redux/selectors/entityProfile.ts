import type { RootState } from '../store';

export const selectEntityProfileUI = (state: RootState) => state.entityProfileUI;
export const selectEntityProfileActiveTab = (state: RootState) => state.entityProfileUI.activeTab;
export const SelectLastVisitedEntityProfileId = (state: RootState) =>
  state.entityProfileUI.lastVisitedEntityProfileId;
export const selectCurrentEntityProfile = (state: RootState) => state.entityProfile;
export const selectEntityProfilesList = (state: RootState) => state.entityProfilesList;
