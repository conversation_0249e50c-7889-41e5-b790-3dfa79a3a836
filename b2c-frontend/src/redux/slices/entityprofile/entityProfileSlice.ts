/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { createSlice } from '@reduxjs/toolkit';
import { EntityProfileStateI } from './types';

const initialState: EntityProfileStateI = {
  entityProfileId: '',
  name: '',
  entity: {
    id: '',
    dataType: 'master',
  },
  avatar: null,
  description: null,
  overview: null,
  foundedAt: '',
  website: null,
  loading: false,
  error: null,
  isActive: false,
  role: '',
};

const entityProfileSlice = createSlice({
  name: 'entityProfile',
  initialState,
  reducers: {
    resetEntityProfileState: () => initialState,
    setSelfEntityProfileData: (state, action) => {
      //set everything except id
    },
    setEntityProfileBasicData: (state, action) => {
      state.entityProfileId = action.payload.id;
      ((state.avatar = action.payload.avatar),
        (state.name = action.payload.name),
        (state.role = action.payload.role));
      state.entity = {
        id: action.payload.entity.id,
        dataType: action.payload.entity.dataType,
      };
    },
  },
});

export const { resetEntityProfileState, setSelfEntityProfileData, setEntityProfileBasicData } =
  entityProfileSlice.actions;

export default entityProfileSlice.reducer;
