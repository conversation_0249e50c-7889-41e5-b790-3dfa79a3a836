import { createSlice, type PayloadAction, createAsyncThunk } from '@reduxjs/toolkit';
import { fetchEntityProfilesForUserAPI } from '@/src/networks/entityProfile/fetchEntityProfilesForUser';

export type CachedEntityProfile = {
  id: string;
  name: string;
  avatar: string | null;
  role: string;
  isVerified: boolean;
};

type EntityProfilesListState = {
  items: CachedEntityProfile[];
  loading: boolean;
  error: string | null;
  lastFetched: number | null;
};

const initialState: EntityProfilesListState = {
  items: [],
  loading: false,
  error: null,
  lastFetched: null,
};

export const fetchEntityProfiles = createAsyncThunk(
  'entityProfilesList/fetch',
  async (_, { getState }) => {
    const state = getState() as any;
    const { items, lastFetched } = state.entityProfilesList;
    
    // Use cache if data exists and was fetched within the last 5 minutes
    const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;
    if (items.length > 0 && lastFetched && lastFetched > fiveMinutesAgo) {
      return items;
    }
    
    const res = await fetchEntityProfilesForUserAPI();
    return res.map((p: any) => ({
      id: p.id,
      name: p.name,
      avatar: p.avatar ?? null,
      role: p.role,
      isVerified: !!p.isVerified,
    })) as CachedEntityProfile[];
  }
);

export const refreshEntityProfiles = createAsyncThunk(
  'entityProfilesList/refresh',
  async () => {
    const res = await fetchEntityProfilesForUserAPI();
    return res.map((p: any) => ({
      id: p.id,
      name: p.name,
      avatar: p.avatar ?? null,
      role: p.role,
      isVerified: !!p.isVerified,
    })) as CachedEntityProfile[];
  }
);

const entityProfilesListSlice = createSlice({
  name: 'entityProfilesList',
  initialState,
  reducers: {
    setEntityProfiles(state, action: PayloadAction<CachedEntityProfile[]>) {
      state.items = action.payload;
      state.lastFetched = Date.now();
      state.error = null;
    },
    clearEntityProfiles(state) {
      state.items = [];
      state.lastFetched = null;
      state.error = null;
    },
  },
  extraReducers(builder) {
    builder
      .addCase(fetchEntityProfiles.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchEntityProfiles.fulfilled, (state, action) => {
        state.items = action.payload;
        state.loading = false;
        state.lastFetched = Date.now();
      })
      .addCase(fetchEntityProfiles.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message ?? 'Failed to load profiles';
      })
      .addCase(refreshEntityProfiles.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(refreshEntityProfiles.fulfilled, (state, action) => {
        state.items = action.payload;
        state.loading = false;
        state.lastFetched = Date.now();
      })
      .addCase(refreshEntityProfiles.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message ?? 'Failed to load profiles';
      });
  },
});

export const { setEntityProfiles, clearEntityProfiles } = entityProfilesListSlice.actions;

export default entityProfilesListSlice.reducer;
