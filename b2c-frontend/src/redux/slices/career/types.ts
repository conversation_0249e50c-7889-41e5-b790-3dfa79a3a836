export type CareerPage = 'jobs' | 'myJobs' | 'jobPosts' | 'applicants';
export type JobsFilterCategory = 'locations' | 'designations' | 'internetLimits' | 'shipTypes';
export type JobPostsFilterCategory = 'designations' | 'shipTypes';
export type ApplicantsFilterCategory = 'locations' | 'designations' | 'yearsOfExperiences';

export type FilterCategory = JobsFilterCategory | JobPostsFilterCategory | ApplicantsFilterCategory;

export type CareerState = {
  jobs: Record<
    JobsFilterCategory,
    {
      values: IdLabelCountDataTypeI[];
      selectedValues: IdLabelCountDataTypeI[];
    }
  >;
  myJobs: Record<
    JobsFilterCategory,
    {
      values: IdLabelCountDataTypeI[];
      selectedValues: IdLabelCountDataTypeI[];
    }
  >;
  jobPosts: Record<
    JobPostsFilterCategory,
    {
      values: IdLabelCountDataTypeI[];
      selectedValues: IdLabelCountDataTypeI[];
    }
  >;
  applicants: Record<
    ApplicantsFilterCategory,
    {
      values: IdLabelCountDataTypeI[];
      selectedValues: IdLabelCountDataTypeI[];
    }
  >;
};

export type IdLabelCountDataTypeI = {
  id: string;
  label: string;
  count: number;
  dataType: string;
};

export type setFilterPayload = {
  page: CareerPage;
  filters: {
    locations?: IdLabelCountDataTypeI[];
    designations?: IdLabelCountDataTypeI[];
    benefits?: IdLabelCountDataTypeI[];
    shipTypes?: IdLabelCountDataTypeI[];
    yearsOfExperiences?: IdLabelCountDataTypeI[];
  };
};

export type toggleFilterPayload = {
  page: string;
  filter: string;
  value: IdLabelCountDataTypeI;
  selected: boolean;
};
