import { createSlice, type PayloadAction } from '@reduxjs/toolkit';
import { CareerState, IdLabelCountDataTypeI, setFilterPayload, toggleFilterPayload } from './types';

const initialState: CareerState = {
  jobs: {
    locations: {
      values: [],
      selectedValues: [],
    },
    designations: {
      values: [],
      selectedValues: [],
    },
    internetLimits: {
      values: [],
      selectedValues: [],
    },
    shipTypes: {
      values: [],
      selectedValues: [],
    },
  },
  myJobs: {
    locations: {
      values: [],
      selectedValues: [],
    },
    designations: {
      values: [],
      selectedValues: [],
    },
    internetLimits: {
      values: [],
      selectedValues: [],
    },
    shipTypes: {
      values: [],
      selectedValues: [],
    },
  },
  jobPosts: {
    designations: {
      values: [],
      selectedValues: [],
    },
    shipTypes: {
      values: [],
      selectedValues: [],
    },
  },
  applicants: {
    locations: {
      values: [],
      selectedValues: [],
    },
    designations: {
      values: [],
      selectedValues: [],
    },
    yearsOfExperiences: {
      values: [],
      selectedValues: [],
    },
  },
};

const careerSlice = createSlice({
  name: 'career',
  initialState,
  reducers: {
    setFilters: (state, action: PayloadAction<setFilterPayload>) => {
      const { page, filters } = action.payload;
      Object.entries(filters).forEach(([category, newValues]) => {
        const currentValues = state[page][category].values || [];
        const currentSelectedValues = state[page][category].selectedValues || [];

        const allExistingIds = new Set([
          ...currentValues.map((item) => item.id),
          ...currentSelectedValues.map((item) => item.id),
        ]);

        const uniqueNewValues = newValues.filter((item) => !allExistingIds.has(item.id));
        state[page][category].values = [...currentValues, ...uniqueNewValues];
      });
    },
    toggleFilter: (state, action: PayloadAction<toggleFilterPayload>) => {
      const { page, filter, value, selected } = action.payload;
      if (selected) {
        state[page][filter].selectedValues = state[page][filter].selectedValues.filter(
          (prev) => prev.id !== value.id,
        );
        state[page][filter].values.push(value);
      } else {
        state[page][filter].values = state[page][filter].values.filter(
          (prev) => prev.id !== value.id,
        );
        state[page][filter].selectedValues.push(value);
      }
    },
    clearAllFilters: (state, action: PayloadAction<{ page: string }>) => {
      const { page } = action.payload;
      if (state[page]) {
        Object.keys(state[page]).forEach((filter) => {
          state[page][filter].selectedValues = [];
        });
      }
    },
  },
});

export const { setFilters, toggleFilter, clearAllFilters } = careerSlice.actions;

export default careerSlice.reducer;
