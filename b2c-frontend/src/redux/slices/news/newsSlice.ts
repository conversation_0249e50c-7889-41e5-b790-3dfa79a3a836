/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { createSlice, createAsyncThunk, type PayloadAction } from '@reduxjs/toolkit';
import { fetchNewsAPI, fetchNewsTopicsAPI } from '@/src/networks/news/news';
import type {
  NewsItemI,
  NewsTopicI,
  NewsFetchManyPayloadI,
  NewsFetchManyResultI,
  NewsTopicsFetchResultI,
} from '@/src/networks/news/types';

export interface NewsFiltersI {
  topicId: string | null;
  sortBy: 'publishedDate' | 'scrapedAt';
  sortOrder: 'asc' | 'desc';
}

export interface NewsPaginationI {
  nextCursorId: string | null;
  hasMore: boolean;
}

export interface NewsStateI {
  news: NewsItemI[];
  topics: NewsTopicI[];
  filters: NewsFiltersI;
  pagination: NewsPaginationI;
  loading: {
    news: boolean;
    topics: boolean;
  };
  error: {
    news: string | null;
    topics: string | null;
  };
}

const initialFilters: NewsFiltersI = {
  topicId: null,
  sortBy: 'publishedDate',
  sortOrder: 'desc',
};

const initialPagination: NewsPaginationI = {
  nextCursorId: null,
  hasMore: true,
};

const initialState: NewsStateI = {
  news: [],
  topics: [],
  filters: initialFilters,
  pagination: initialPagination,
  loading: {
    news: true,
    topics: false,
  },
  error: {
    news: null,
    topics: null,
  },
};

// Async thunks
export const fetchNews = createAsyncThunk<
  NewsFetchManyResultI,
  { refresh?: boolean },
  { state: { news: NewsStateI } }
>('news/fetchNews', async ({ refresh = false }, { getState }) => {
  const state = getState().news;

  const payload: NewsFetchManyPayloadI = {
    pageSize: '10',
    sortBy: state.filters.sortBy,
    sortOrder: state.filters.sortOrder,
    ...(state.filters.topicId && { topicId: state.filters.topicId }),
  };

  if (!refresh && state.pagination.nextCursorId) {
    payload.cursorId = state.pagination.nextCursorId;
  }

  return await fetchNewsAPI(payload);
});

export const fetchNewsTopics = createAsyncThunk<NewsTopicsFetchResultI>(
  'news/fetchNewsTopics',
  async () => {
    return await fetchNewsTopicsAPI();
  },
);

const newsSlice = createSlice({
  name: 'news',
  initialState,
  reducers: {
    setNewsFilters: (state, action: PayloadAction<Partial<NewsFiltersI>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearNews: (state) => {
      state.news = [];
      state.pagination = initialPagination;
    },
    resetNewsState: () => initialState,
  },
  extraReducers: (builder) => {
    // Fetch News
    builder
      .addCase(fetchNews.pending, (state) => {
        state.loading.news = true;
        state.error.news = null;
      })
      .addCase(fetchNews.fulfilled, (state, action) => {
        state.loading.news = false;
        const { data, nextCursorId } = action.payload;

        // If this is a refresh or first load, replace all news
        if (!state.pagination.nextCursorId) {
          state.news = data;
        } else {
          // Otherwise, append new news items
          const existingIds = new Set(state.news.map((item) => item.id));
          const newItems = data.filter((item) => !existingIds.has(item.id));
          state.news.push(...newItems);
        }

        state.pagination.nextCursorId = nextCursorId;
        state.pagination.hasMore = !!nextCursorId;
      })
      .addCase(fetchNews.rejected, (state, action) => {
        state.loading.news = false;
        state.error.news = action.error.message || 'Failed to fetch news';
      });

    // Fetch News Topics
    builder
      .addCase(fetchNewsTopics.pending, (state) => {
        state.loading.topics = true;
        state.error.topics = null;
      })
      .addCase(fetchNewsTopics.fulfilled, (state, action) => {
        state.loading.topics = false;
        state.topics = action.payload.data;
      })
      .addCase(fetchNewsTopics.rejected, (state, action) => {
        state.loading.topics = false;
        state.error.topics = action.error.message || 'Failed to fetch news topics';
      });
  },
});

export const { setNewsFilters, clearNews, resetNewsState } = newsSlice.actions;
export default newsSlice.reducer;
