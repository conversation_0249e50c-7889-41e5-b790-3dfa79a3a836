/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { persistReducer, persistStore } from 'redux-persist';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { configureStore } from '@reduxjs/toolkit';
import aboutReducer from './slices/about/aboutSlice';
import aiChatReducer from './slices/aichat/aiChatSlice';
import announcementReducer from './slices/announcement/announcementSlice';
import careerReducer from './slices/career/careerSlice';
import communityReducer from './slices/community/communitySlice';
import contentReducer from './slices/content/contentSlice';
import entityProfileListReducer from './slices/entityprofile/entityProfileListSlice';
import entityProfileReducer from './slices/entityprofile/entityProfileSlice';
import entityProfileUIReducer from './slices/entityprofile/entityProfileUISlice';
import searchReducer from './slices/entitysearch/searchSlice';
import experienceReducer from './slices/experience/experienceSlice';
import forumReducer from './slices/forum/forumSlice';
import forumSearchReducer from './slices/forumsearch/forumSearchSlice';
import globalSearchReducer from './slices/globalsearch/globalSearchSlice';
import newsReducer from './slices/news/newsSlice';
import profileUiReducer from './slices/profile/profileUiSlice';
import questionReducer from './slices/question/questionSlice';
import userReducer from './slices/user/userSlice';
import voteReducer from './slices/vote/voteSlice';

const userPersistConfig = {
  key: 'user',
  storage: AsyncStorage,
};

const globalSearchPersistConfig = {
  key: 'globalsearch',
  storage: AsyncStorage,
};

const forumSearchPersistConfig = {
  key: 'forumsearch',
  storage: AsyncStorage,
};

const aiChatPersistConfig = {
  key: 'aichat',
  storage: AsyncStorage,
};

const contentPersistConfig = {
  key: 'content',
  storage: AsyncStorage,
  whitelist: ['cachedPosts'],
};

const entityProfilePersistConfig = {
  key: 'entityprofile',
  storage: AsyncStorage,
};

const persistedUserReducer = persistReducer(userPersistConfig, userReducer);
const persistedGlobalSearchReducer = persistReducer(globalSearchPersistConfig, globalSearchReducer);
const persistedForumSearchReducer = persistReducer(forumSearchPersistConfig, forumSearchReducer);
const persistedAIChatReducer = persistReducer(aiChatPersistConfig, aiChatReducer);
const persistedContentReducer = persistReducer(contentPersistConfig, contentReducer);
const persistedEntityProfileReducer = persistReducer(
  entityProfilePersistConfig,
  entityProfileReducer,
);

export const store = configureStore({
  reducer: {
    user: persistedUserReducer,
    globalsearch: persistedGlobalSearchReducer,
    forumsearch: persistedForumSearchReducer,
    aichat: persistedAIChatReducer,
    content: persistedContentReducer,
    search: searchReducer,
    experience: experienceReducer,
    about: aboutReducer,
    profileUi: profileUiReducer,
    forum: forumReducer,
    question: questionReducer,
    vote: voteReducer,
    community: communityReducer,
    announcement: announcementReducer,
    entityProfileUI: entityProfileUIReducer,
    entityProfile: persistedEntityProfileReducer,
    entityProfilesList: entityProfileListReducer,
    career: careerReducer,
    news: newsReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
    }),
});

export const persistor = persistStore(store);
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
