/**
 * Formats a date string or Date object to "Day MonthName, Year" format
 * Example: "15 December, 2023"
 */
export const formatNewsDate = (dateString: string | Date): string => {
  try {
    const date = typeof dateString === 'string' ? new Date(dateString) : dateString;

    // Check if date is valid
    if (isNaN(date.getTime())) {
      return 'Invalid date';
    }

    const day = date.getDate();
    const year = date.getFullYear();

    // Get full month name
    const monthNames = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ];

    const monthName = monthNames[date.getMonth()];

    return `${day} ${monthName}, ${year}`;
  } catch (error) {
    console.warn('Error formatting date:', error);
    return 'Invalid date';
  }
};
