/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import {
  createNavigationContainerRef,
  StackActions,
  CommonActions,
} from '@react-navigation/native';
import { AppStackParamListI } from '@/src/navigation/types';
import useStorage from '@/src/hooks/storage';
import { containsPersonalEmailDomain } from '../mail/checkGenericMail';

export const navigationRef = createNavigationContainerRef<AppStackParamListI>();

type RouteName = keyof AppStackParamListI;

export const navigate = <T extends RouteName>(name: T, params?: AppStackParamListI[T]) => {
  if (navigationRef.isReady()) {
    navigationRef.navigate(name as any, params);
  }
};

export const reset = <T extends RouteName>(name: T, params?: AppStackParamListI[T]) => {
  if (navigationRef.isReady()) {
    navigationRef.dispatch(
      CommonActions.reset({
        index: 0,
        routes: [{ name, params }],
      }),
    );
  }
};

export const replace = <T extends RouteName>(name: T, params?: AppStackParamListI[T]) => {
  if (navigationRef.isReady()) {
    navigationRef.dispatch(StackActions.replace(name, params));
  }
};

export const push = <T extends RouteName>(name: T, params?: AppStackParamListI[T]) => {
  if (navigationRef.isReady()) {
    navigationRef.dispatch(StackActions.push(name, params));
  }
};

export const pop = (count = 1) => {
  if (navigationRef.isReady()) {
    navigationRef.dispatch(StackActions.pop(count));
  }
};

export const popToTop = () => {
  if (navigationRef.isReady()) {
    navigationRef.dispatch(StackActions.popToTop());
  }
};

export const goBack = () => {
  if (navigationRef.isReady() && navigationRef.canGoBack()) {
    navigationRef.goBack();
  }
};

export const setParams = <T extends RouteName>(params: Partial<AppStackParamListI[T]>) => {
  if (navigationRef.isReady()) {
    navigationRef.dispatch(CommonActions.setParams(params));
  }
};

type NavigateInput = {
  isUsernameSaved: boolean;
  isPersonalDetailsSaved: boolean;
  isWorkDetailsSaved: boolean;
  email: string;
  isEmailVerified: boolean;
  profileId: string;
  isPrivacyPolicyAccepted: boolean;
  isReferred: boolean;
};

export const navigateBasedOnUserState = async ({
  isUsernameSaved,
  isPersonalDetailsSaved,
  isWorkDetailsSaved,
  email,
  isEmailVerified,
  profileId,
  isPrivacyPolicyAccepted,
  isReferred,
}: NavigateInput) => {
  const { hasSkippedReferral } = useStorage();
  const skippedReferral = await hasSkippedReferral();
  switch (true) {
    case !isEmailVerified:
      navigate('VerifyEmail', { email, profileId });
      break;
    case !isPrivacyPolicyAccepted:
      navigate('PolicyAcceptance');
      break;
    case !isUsernameSaved && !skippedReferral && !isReferred:
      navigate('ReferralCode');
      break;
    case !isUsernameSaved:
      navigate('SetUsername');
      break;
    case !isPersonalDetailsSaved || !isWorkDetailsSaved:
      navigate('AddUserDetailScreen');
      break;
    default:
      navigate('Home');
  }
};

type NavigateEntityProfileI = {
  verificationStatus: boolean;
  email: string;
  entityProfileId: string;
};

export const navigateBasedOnEntityProfileVerificationState = async ({
  email,
  entityProfileId,
  verificationStatus,
}: NavigateEntityProfileI) => {
  switch (verificationStatus) {
    case false:
      navigate('VerifyEntityEmail', {
        email,
        entityProfileId,
        emailType: containsPersonalEmailDomain(email) ? 'PERSONAL' : 'WORK',
      });
      break;
    case true:
      break;
    default:
      navigate('Home');
  }
};
