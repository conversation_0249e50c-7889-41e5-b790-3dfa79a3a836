import React, { useCallback, useEffect, useMemo, useState } from 'react';
import {
  ActivityIndicator,
  FlatList,
  RefreshControl,
  ScrollView,
  Text,
  TouchableOpacity,
  Pressable,
  View,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { StackNavigationProp, StackScreenProps } from '@react-navigation/stack';
import { useDispatch, useSelector } from 'react-redux';
import BackButton from '@/src/components/BackButton';
import NotFound from '@/src/components/NotFound';
import SafeArea from '@/src/components/SafeArea';
import FloatingActionButton from '@/src/components/FloatingActionButton';
import { selectNewsTopics, selectNewsLoading } from '@/src/redux/selectors/news';
import { fetchForumQuestionDetail } from '@/src/redux/slices/forum/forumSlice';
import { fetchNewsTopics } from '@/src/redux/slices/news/newsSlice';
import type { AppDispatch } from '@/src/redux/store';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';
import ForumPost from '@/src/screens/Forum/components/ForumPost';
import { transformQuestionToForumPost } from '@/src/screens/Forum/components/ForumPost/utils';
import ForumPostSkeleton from '@/src/screens/Forum/components/ForumPostSkelton';
import type { NewsTopicI } from '@/src/networks/news/types';
import { fetchForumQuestionsAPI } from '@/src/networks/question/question';
import type {
  ForumQuestionFetchManyPayloadI,
  ForumQuestionI,
  ForumQuestionResultI,
} from '@/src/networks/question/types';

const POSTS_PER_PAGE = 10;
const INITIAL_NEWS_TOPICS_COUNT = 6;

const ExploreForumScreen: React.FC = () => {
  const route = useRoute<StackScreenProps<LearnCollabStackParamsListI, 'ExploreForum'>['route']>();
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();
  const dispatch = useDispatch<AppDispatch>();

  // News topics state
  const newsTopics = useSelector(selectNewsTopics);
  const newsLoading = useSelector(selectNewsLoading);
  const [showAllNewsTopics, setShowAllNewsTopics] = useState(false);

  // Data state
  const [forumQuestions, setForumQuestions] = useState<ForumQuestionI[]>([]);
  const [paginationCursor, setPaginationCursor] = useState<string | null>(null);
  const [hasMorePages, setHasMorePages] = useState(true);

  // Loading states
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isInitialLoading, setIsInitialLoading] = useState(true);

  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const buildForumQueryParams = useCallback(
    (cursorDate: string | null, shouldResetCursor: boolean): ForumQuestionFetchManyPayloadI => {
      const routeParams = route.params;
      const questionType = routeParams?.type ?? (routeParams?.topicId ? 'NORMAL' : undefined);

      return {
        pageSize: POSTS_PER_PAGE.toString(),
        cursorDate: shouldResetCursor ? null : cursorDate,
        ...(questionType && { type: questionType }),
        ...(routeParams?.topicId &&
          routeParams?.topicDataType && {
            topicId: routeParams.topicId,
            topicDataType: routeParams.topicDataType,
          }),
        ...(routeParams?.equipmentCategory && {
          equipmentCategoryId: routeParams.equipmentCategory.id,
          equipmentCategoryDataType: routeParams.equipmentCategory.dataType,
        }),
        ...(routeParams?.equipmentManufacturer && {
          equipmentManufacturerId: routeParams.equipmentManufacturer.id,
          equipmentManufacturerDataType: routeParams.equipmentManufacturer.dataType,
        }),
        ...(routeParams?.equipmentModel && {
          equipmentModelId: routeParams.equipmentModel.id,
          equipmentModelDataType: routeParams.equipmentModel.dataType,
        }),
      };
    },
    [route.params],
  );

  const loadForumQuestions = useCallback(
    async (shouldRefresh: boolean) => {
      try {
        setErrorMessage(null);

        if (shouldRefresh) {
          setIsRefreshing(true);
        } else if (forumQuestions.length === 0) {
          setIsInitialLoading(true);
        } else {
          setIsLoadingMore(true);
        }

        const queryParams = buildForumQueryParams(paginationCursor, shouldRefresh);
        const apiResponse = await fetchForumQuestionsAPI(queryParams);

        if (shouldRefresh) {
          // Replace all questions on refresh
          setForumQuestions(apiResponse.data);
        } else {
          // Append new questions and deduplicate
          const existingQuestionIds = new Set(forumQuestions.map((question) => question.id));
          const newUniqueQuestions = apiResponse.data.filter(
            (question) => !existingQuestionIds.has(question.id),
          );
          setForumQuestions((prevQuestions) => [...prevQuestions, ...newUniqueQuestions]);
        }

        // Update pagination state
        setPaginationCursor(apiResponse.nextCursorDate);

        // Calculate if there are more pages available
        const totalLoadedQuestions = shouldRefresh
          ? apiResponse.data.length
          : forumQuestions.length + apiResponse.data.length;

        const hasMoreData =
          totalLoadedQuestions < apiResponse.total && Boolean(apiResponse.nextCursorDate);
        setHasMorePages(hasMoreData);
      } catch (error) {
        console.error('Failed to load forum questions:', error);
        setErrorMessage('Failed to load forum posts. Please try again.');
      } finally {
        setIsLoadingMore(false);
        setIsRefreshing(false);
        setIsInitialLoading(false);
      }
    },
    [buildForumQueryParams, paginationCursor, forumQuestions],
  );

  // Fetch news topics
  useEffect(() => {
    if (newsTopics.length === 0) {
      dispatch(fetchNewsTopics());
    }
  }, [dispatch, newsTopics.length]);

  // Reset state and load initial data when route params change
  useEffect(() => {
    setForumQuestions([]);
    setPaginationCursor(null);
    setHasMorePages(true);
    setErrorMessage(null);
    loadForumQuestions(true);
  }, [route.params]);

  const handleRefresh = useCallback(() => {
    loadForumQuestions(true);
  }, [loadForumQuestions]);

  const handleLoadMore = useCallback(() => {
    if (!isLoadingMore && hasMorePages && !errorMessage) {
      loadForumQuestions(false);
    }
  }, [isLoadingMore, hasMorePages, errorMessage, loadForumQuestions]);

  const handleRetry = useCallback(() => {
    if (forumQuestions.length === 0) {
      loadForumQuestions(true);
    } else {
      loadForumQuestions(false);
    }
  }, [forumQuestions.length, loadForumQuestions]);

  // News topic handlers
  const handleNewsTopicPress = useCallback(
    (topic: NewsTopicI) => {
      navigation.navigate('ExploreNews', { topicId: topic.id });
    },
    [navigation],
  );

  const handleSeeMoreNewsTopics = useCallback(() => {
    setShowAllNewsTopics(true);
  }, []);

  const transformedForumPosts = useMemo(
    () =>
      forumQuestions.map((question) =>
        transformQuestionToForumPost(question as unknown as ForumQuestionResultI, question.media),
      ),
    [forumQuestions],
  );

  const handleForumPostSelection = useCallback(
    async (id: string) => {
      try {
        await dispatch(fetchForumQuestionDetail({ questionId: id })).unwrap();
        navigation.navigate('ForumAnswers', { postId: id });
      } catch (error) {
        console.error('Failed to fetch question detail:', error);
      }
    },
    [dispatch, navigation],
  );

  const renderForumPost = useCallback(
    ({ item: forumPost }: { item: ReturnType<typeof transformQuestionToForumPost> }) => (
      <ForumPost
        post={{
          ...forumPost,
          onPress: () => handleForumPostSelection(forumPost.postId),
          community: forumPost.community?.name,
        }}
      />
    ),
    [handleForumPostSelection],
  );

  const handleCreateQuestion = useCallback(() => {
    navigation.navigate('CommunityQuestion', { id: '' });
  }, [navigation]);

  const renderLoadingFooter = useCallback(() => {
    if (!isLoadingMore) return null;

    return (
      <View className="py-4 items-center">
        <ActivityIndicator size="small" color="#666" />
      </View>
    );
  }, [isLoadingMore]);

  const renderErrorMessage = useCallback(() => {
    if (!errorMessage || isInitialLoading) return null;

    return (
      <View className="py-4 px-4 items-center">
        <Text className="text-red-500 text-center mb-2">{errorMessage}</Text>
        <TouchableOpacity onPress={handleRetry} className="bg-blue-500 px-4 py-2 rounded">
          <Text className="text-white font-medium">Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }, [errorMessage, isInitialLoading]);

  const renderInitialLoadingSkeleton = useCallback(
    () => (
      <View className="flex-1">
        {Array.from({ length: 5 }).map((_, index) => (
          <ForumPostSkeleton key={`forum-skeleton-${index}`} />
        ))}
      </View>
    ),
    [],
  );

  // News topics render functions
  const displayedNewsTopics = useMemo(
    () => (showAllNewsTopics ? newsTopics : newsTopics.slice(0, INITIAL_NEWS_TOPICS_COUNT)),
    [showAllNewsTopics, newsTopics],
  );

  const renderNewsTopics = useCallback(() => {
    if (newsLoading.topics) {
      return (
        <View className="py-4 items-center">
          <ActivityIndicator size="small" color="#448600" />
          <Text className="mt-2 text-gray-600 text-sm">Loading news topics...</Text>
        </View>
      );
    }

    if (newsTopics.length === 0) {
      return (
        <View className="py-4 items-center">
          <Text className="text-gray-500 text-center text-sm">No news topics available</Text>
        </View>
      );
    }

    return (
      <View className="p-4 bg-gray-50">
        <Text className="text-labelBlack text-lg font-medium leading-5 mb-4">
          Explore News Topics
        </Text>

        <View className="flex-row flex-wrap gap-2 mb-4">
          {displayedNewsTopics.map((topic) => (
            <TouchableOpacity
              key={topic.id}
              onPress={() => handleNewsTopicPress(topic)}
              className="bg-white border border-chipGray rounded-full py-3 px-4"
              activeOpacity={0.7}
            >
              <Text className="text-center text-black text-sm">{topic.name}</Text>
            </TouchableOpacity>
          ))}
        </View>

        {!showAllNewsTopics && newsTopics.length > INITIAL_NEWS_TOPICS_COUNT && (
          <Pressable onPress={handleSeeMoreNewsTopics}>
            <Text className="text-primaryGreen text-base font-medium">See More</Text>
          </Pressable>
        )}
      </View>
    );
  }, [
    newsLoading.topics,
    newsTopics.length,
    displayedNewsTopics,
    handleNewsTopicPress,
    showAllNewsTopics,
    handleSeeMoreNewsTopics,
  ]);

  if (isInitialLoading && forumQuestions.length === 0) {
    return (
      <SafeArea>
        <BackButton onBack={() => navigation.goBack()} label="Explore" />
        <View className="flex-1 bg-white">{renderInitialLoadingSkeleton()}</View>
        <FloatingActionButton
          context="CREATE_QUESTION"
          onPress={handleCreateQuestion}
          visible={true}
        />
      </SafeArea>
    );
  }

  return (
    <SafeArea>
      <BackButton
        onBack={() => navigation.goBack()}
        label="Explore Forums"
        labelClassname="font-semibold text-2xl"
      />
      <View className="flex-1 bg-white">
        <ScrollView
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl refreshing={isRefreshing} onRefresh={handleRefresh} tintColor="#666" />
          }
        >
          {/* News Topics Section */}
          {renderNewsTopics()}

          {/* Forum Posts Section */}
          <View className="p-4">
            <Text className="text-labelBlack text-lg font-medium leading-5 mb-4">
              Forum Questions
            </Text>

            {transformedForumPosts.length > 0 ? (
              transformedForumPosts.map((forumPost) => (
                <View key={forumPost.postId}>{renderForumPost({ item: forumPost })}</View>
              ))
            ) : !isInitialLoading && !errorMessage ? (
              <NotFound
                title="No questions found"
                subtitle="Try searching for a different keyword"
                fullScreen={false}
                className="py-4"
                titleClassName="text-base font-medium"
              />
            ) : null}

            {renderLoadingFooter()}
          </View>

          {/* Load More Button */}
          {hasMorePages && !isLoadingMore && transformedForumPosts.length > 0 && (
            <View className="px-4 pb-4">
              <TouchableOpacity
                onPress={handleLoadMore}
                className="bg-primaryGreen py-3 px-6 rounded-full items-center"
              >
                <Text className="text-white font-medium">Load More</Text>
              </TouchableOpacity>
            </View>
          )}
        </ScrollView>
        {renderErrorMessage()}
      </View>
      <FloatingActionButton
        context="CREATE_QUESTION"
        onPress={handleCreateQuestion}
        visible={true}
      />
    </SafeArea>
  );
};

export default ExploreForumScreen;
