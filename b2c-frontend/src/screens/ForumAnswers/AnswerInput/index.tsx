/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  TextInput,
  Pressable,
  ActivityIndicator,
  Text,
  Keyboard,
  Platform,
  Animated,
} from 'react-native';
import BottomSheet from '@/src/components/Bottomsheet';
import { OptionItem, OptionsMenu } from '@/src/components/OptionsMenu';
import Attachment from '@/src/assets/svgs/Attachment';
import Send from '@/src/assets/svgs/Send';
import AttachmentPreview from './components/AttachmentPreview';
import type { AnswerInputPropsI } from './types';
import { MAX_ANSWER_LENGTH, useAnswerInput } from './useHook';

const AnswerInput = ({ onSubmit }: AnswerInputPropsI) => {
  const {
    answer,
    setAnswer,
    isSubmitting,
    attachments,
    inputRef,
    canSubmit,
    isNearLimit,
    handleAttachments,
    handleDeleteAttachment,
    handleSend,
    isAttachmentOptionsVisible,
    setIsAttachmentOptionsVisible,
    handleModalHide,
    handleGalleryPress,
    handleFilesPress,
    handleCameraPress,
  } = useAnswerInput(onSubmit);
  const translateY = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', (e) => {
      if (Platform.OS === 'android') {
        const shouldApplyKeyboardAdjustment = Platform.Version >= 35;
        if (shouldApplyKeyboardAdjustment) {
          Animated.timing(translateY, {
            toValue: -e.endCoordinates.height,
            duration: 250,
            useNativeDriver: true,
          }).start();
        }
      }
    });

    const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () => {
      if (Platform.OS === 'android') {
        const shouldApplyKeyboardAdjustment = Platform.Version >= 35;
        if (shouldApplyKeyboardAdjustment) {
          Animated.timing(translateY, {
            toValue: 0,
            duration: 250,
            useNativeDriver: true,
          }).start();
        }
      }
    });

    return () => {
      keyboardDidShowListener?.remove();
      keyboardDidHideListener?.remove();
    };
  }, [translateY]);

  return (
    <Animated.View
      className="bg-white border-t border-gray-200 p-4"
      style={{
        paddingBottom: 8,
        transform: [{ translateY }],
      }}
    >
      {attachments.length > 0 && (
        <AttachmentPreview attachments={attachments} onRemove={handleDeleteAttachment} />
      )}
      <View className="flex-row items-end gap-3">
        <Pressable
          onPress={handleAttachments}
          className="w-11 h-11 rounded-full items-center justify-center bg-gray-200"
          style={{ marginBottom: 22 }}
        >
          <Attachment width={3} height={3} />
        </Pressable>

        <View className="flex-1">
          <TextInput
            ref={inputRef}
            className="bg-gray-100 rounded-2xl text-black px-4 py-3 text-base min-h-[44px] max-h-[120px]"
            placeholder="Write your answer..."
            placeholderTextColor="#9CA3AF"
            value={answer}
            onChangeText={setAnswer}
            maxLength={MAX_ANSWER_LENGTH}
            multiline
            autoCorrect
            editable={!isSubmitting}
            textAlignVertical="top"
            returnKeyType="default"
            blurOnSubmit={false}
          />
          <View className="flex-row justify-start items-center mt-2 px-2">
            <Text className={`text-xs ${isNearLimit ? 'text-orange-500' : 'text-gray-500'}`}>
              {answer.length}/{MAX_ANSWER_LENGTH}
            </Text>
          </View>
        </View>

        <Pressable
          onPress={handleSend}
          disabled={!canSubmit}
          className={`w-11 h-11 rounded-full items-center justify-center ${
            canSubmit ? 'bg-green-800' : 'bg-gray-400'
          }`}
          style={{ marginBottom: 22 }}
        >
          {isSubmitting ? (
            <ActivityIndicator size="small" color="white" />
          ) : (
            <Send width={1.8} height={1.8} color="white" strokeWidth={2} />
          )}
        </Pressable>
      </View>

      <BottomSheet
        visible={isAttachmentOptionsVisible}
        onClose={() => setIsAttachmentOptionsVisible(false)}
        onModalHide={handleModalHide}
        height={Platform.OS === 'ios' ? 180 : 140}
      >
        <OptionsMenu>
          {Platform.OS === 'ios' && (
            <>
              <OptionItem label="Photos" onPress={handleGalleryPress} />
              <View className="h-[1px] bg-gray-200" />
            </>
          )}
          <OptionItem label="Files" onPress={handleFilesPress} />
          <View className="h-[1px] bg-gray-200" />
          <OptionItem label="Camera" onPress={handleCameraPress} />
        </OptionsMenu>
      </BottomSheet>
    </Animated.View>
  );
};

export default AnswerInput;
