import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useSelector } from 'react-redux';
import BackButton from '@/src/components/BackButton';
import SafeArea from '@/src/components/SafeArea';
import { selectCurrentEntityProfile } from '@/src/redux/selectors/entityProfile';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { CareerStackParamListI } from '@/src/navigation/types';
import JobPosts from './components/JobPosts';
import FloatingActionButton from '@/src/components/FloatingActionButton';

const JobPostsScreen = () => {
  const currentUser = useSelector(selectCurrentUser);
  const currentEntity = useSelector(selectCurrentEntityProfile);
  const navigation = useNavigation<StackNavigationProp<CareerStackParamListI>>();

  const handleCreateJobPost = () => {
    navigation.navigate('EditJobPost',{
      jobId:undefined
    })
  }

  return (
    <SafeArea>
      <BackButton label="Job Posts" onBack={navigation.goBack} />
      <JobPosts
        isUser={currentUser.isActive}
        profileId={currentUser.profileId}
        entityProfileId={currentEntity.entityProfileId}
      />
      <FloatingActionButton
        context="CREATE_JOB_POST"
        onPress={handleCreateJobPost}
        visible={true}
      />
    </SafeArea>
  );
};

export default JobPostsScreen;
