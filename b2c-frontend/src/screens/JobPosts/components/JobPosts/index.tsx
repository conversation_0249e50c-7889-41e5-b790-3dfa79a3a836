import { SetStateAction, useCallback, useState } from 'react';
import { ActivityIndicator, FlatList, RefreshControl, View } from 'react-native';
import CareerFilterBar from '@/src/components/CareerFilterBar';
import Job from '@/src/components/Job';
import Tabs from '@/src/components/Tabs';
import { JobPostsPropsI } from './types';
import { useJobPosts } from './useHook';

const JobPosts = ({ isUser, profileId, entityProfileId }: JobPostsPropsI) => {
  const [activeTab, setActiveTab] = useState('active');

  const tabs = [
    { id: 'active', label: 'Active' },
    { id: 'closed', label: 'Closed' },
    { id: 'expired', label: 'Expired' },
  ];

  const {
    jobPosts,
    searchText,
    filterTabs,
    loading,
    isLoadingMore,
    loadingFilters,
    loadMoreJobPosts,
    setSearchText,
    fetchJobPosts,
    onFilterPress,
  } = useJobPosts(isUser, profileId, entityProfileId, activeTab);

  const onRefresh = useCallback(() => {
    fetchJobPosts(false);
  }, []);

  const renderFooter = () => (
    <View className="pb-4">
      {isLoadingMore && <ActivityIndicator size="small" className="my-4" />}
    </View>
  );

  return (
    <View>
      <CareerFilterBar
        page="jobPosts"
        ellipsesVisible={false}
        onSearchTextChange={setSearchText}
        searchTextValue={searchText}
        className="pb-5"
        filterTabs={filterTabs}
        onFilterPress={onFilterPress}
        onApplyPress={() => fetchJobPosts(false, true)}
        loading={loadingFilters}
      />
      <Tabs
        tabs={tabs}
        activeTab={activeTab}
        onTabChange={setActiveTab as React.Dispatch<SetStateAction<string>>}
      />
      <FlatList
        data={jobPosts}
        renderItem={({ item }) => <Job job={item} />}
        keyExtractor={(item) => item.id}
        showsVerticalScrollIndicator={false}
        onEndReached={loadMoreJobPosts}
        onEndReachedThreshold={0.5}
        ListFooterComponent={renderFooter}
        refreshControl={
          <RefreshControl refreshing={loading} onRefresh={onRefresh} tintColor="#000" />
        }
      />
    </View>
  );
};

export default JobPosts;
