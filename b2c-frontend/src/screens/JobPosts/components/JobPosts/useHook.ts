import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { IdLabelAppliedCountI } from '@/src/components/CareerFilterBar/types';
import { selectActiveFilterNumbers, selectActiveFilters } from '@/src/redux/selectors/careers';
import { selectCurrentEntityProfile } from '@/src/redux/selectors/entityProfile';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { setFilters } from '@/src/redux/slices/career/careerSlice';
import { JobI } from '@/src/screens/Careers/components/Careers/types';
import { fetchFiltersForEntityMembersAPI } from '@/src/networks/jobs/fetchFiltersForEntityMembers';
import { fetchJobsForEntityMemberAPI } from '@/src/networks/jobs/fetchJobsForEntityMember';
import { showToast } from '@/src/utilities/toast';

const PAGE_SIZE = 10;

export const useJobPosts = (
  isUser: boolean,
  profileId: string,
  entityProfileId: string,
  type: string,
) => {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const [loadingFilters,setLoadingFilters] = useState(false);
  const activeFilterNumbers = useSelector(selectActiveFilterNumbers('jobPosts'));
  const currentEntity = useSelector(selectCurrentEntityProfile);
  const currentUser = useSelector(selectCurrentUser);
  const filters = useSelector(selectActiveFilters('jobPosts'));
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [searchText, setSearchText] = useState('');
  const [cursorId, setCursorId] = useState<string | null>(null);
  const [jobPosts, setJobPosts] = useState<JobI[]>([]);

  const filterTabs: IdLabelAppliedCountI[] = [
    {
      id: 'designation',
      label: 'Designation',
      appliedCount: activeFilterNumbers.designations || 0,
    },
    {
      id: 'ship-type',
      label: 'Ship Type',
      appliedCount: activeFilterNumbers.shipTypes || 0,
    },
  ];

  const fetchJobPosts = async (loadMore = false, reset = false) => {
    try {
      if (reset) {
        setCursorId(null);
        setJobPosts([]);
        setLoading(true);
      } else if (loadMore) {
        if (!cursorId || !hasMore) return;
        setIsLoadingMore(true);
      } else {
        setLoading(true);
      }

      const query = {
        isOfficial: isUser ? false : true,
        status: transformStatus(type),
        cursorId: reset ? null : cursorId,
        pageSize: PAGE_SIZE,
      };

      const body = {
        entity: isUser 
        ? null
        : currentEntity.entity,
        designations: filters.designations,
        shipTypes: filters.shipTypes,
      };

      const result = await fetchJobsForEntityMemberAPI(query, body);
      if (loadMore) {
        setJobPosts((prev) => [...prev, ...result.data]);
      } else {
        setJobPosts(result.data);
      }

      setCursorId(result.nextCursorId);
      setHasMore(result.nextCursorId !== null);
    } catch (error) {
    } finally {
      if (loadMore) {
        setIsLoadingMore(false);
      } else {
        setLoading(false);
      }
    }
  };

  useEffect(() => {
    const fetchInitialJobPosts = async () => {
      try {
        setLoading(true);
        const query = {
          isOfficial: isUser ? false : true,
          status: transformStatus(type),
          cursorId: null,
          pageSize: PAGE_SIZE,
        };

        const body = {
          entity: isUser
            ? null
            : currentEntity.entity,
          designations: filters.designations,
          shipTypes: filters.shipTypes,
        };
        const result = await fetchJobsForEntityMemberAPI(query, body);
        setJobPosts(result.data);

        setCursorId(result.nextCursorId);
        setHasMore(result.nextCursorId !== null);
      } catch (error) {
      } finally {
        setLoading(false);
      }
    };
    fetchInitialJobPosts();
  }, [type]);

  const loadMoreJobPosts = () => {
    if (!cursorId || !hasMore || isLoadingMore) return;
    fetchJobPosts(true);
  };

  const onFilterPress = async () => {
    try{
      setLoadingFilters(true)
      const filters = await fetchFiltersForEntityMembersAPI();
      dispatch(
        setFilters({
          page: 'jobPosts',
          filters: filters,
        }),
      );
    }catch(e){
      showToast({
        type:'error',
        message:'Failed to load Filters.Try Again Later'
      })
    }finally{
      setLoadingFilters(false)
    }
    
  };

  return {
    searchText,
    jobPosts,
    filterTabs,
    loading,
    isLoadingMore,
    loadingFilters,
    setSearchText,
    loadMoreJobPosts,
    fetchJobPosts,
    onFilterPress,
  };
};

const transformStatus = (type: string) => {
  const status = {
    active: 'ACTIVE',
    closed: 'DELETED',
    expired: 'EXPIRED',
  };

  return status[type as keyof typeof status];
};
