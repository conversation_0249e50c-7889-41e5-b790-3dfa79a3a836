/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React, { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { View } from 'react-native';
import { RouteProp, useRoute } from '@react-navigation/native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import FloatingActionButton from '@/src/components/FloatingActionButton';
import SafeArea from '@/src/components/SafeArea';
import Tabs from '@/src/components/Tabs';
import { Tab } from '@/src/components/Tabs/types';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';
import CommunityList from '../MyCommunities/components/CommunityList';
import NewsPostList from '../News/components/NewsPostList';
import ForumPostList from './components/ForumPostList';
import TopBar from './components/TopBar';

const tabs: Tab[] = [
  {
    id: 'questions',
    label: 'Questions',
  },
  {
    id: 'communities',
    label: 'Community',
  },
  {
    id: 'news',
    label: 'News & Updates',
  },
];

const ForumFloatingButton = ({ activeTab }: { activeTab: string }) => {
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();

  const [shouldShow, setShouldShow] = useState(true);

  useEffect(() => {
    const unsubscribe = navigation.addListener('state', (e) => {
      const state = e.data.state;
      if (state) {
        const currentRoute = state.routes[state.index];
        const routeName = currentRoute.name;

        setShouldShow(routeName === 'Forum');
      }
    });

    return unsubscribe;
  }, [navigation]);

  const handleCreateQuestion = () => {
    navigation.navigate('CommunityQuestion', { id: '' });
  };

  const handleCreateCommunity = () => {
    navigation.navigate('CreateCommunity');
  };

  if (!shouldShow) {
    return null;
  }

  if (!activeTab) {
    return null;
  }

  // Return appropriate button based on active tab
  switch (activeTab) {
    case 'questions':
      return (
        <FloatingActionButton
          context="CREATE_QUESTION"
          onPress={handleCreateQuestion}
          visible={true}
        />
      );
    case 'communities':
      return (
        <FloatingActionButton
          context="CREATE_COMMUNITY"
          onPress={handleCreateCommunity}
          visible={true}
        />
      );
    case 'news':
      return null;
    default:
      return null;
  }
};

const ForumScreen = () => {
  const route = useRoute<RouteProp<LearnCollabStackParamsListI, 'Forum'>>();
  const initialTab = route.params?.activeTab || 'questions';
  const [activeTab, setActiveTab] = useState<'questions' | 'communities' | 'news'>(initialTab);

  useEffect(() => {
    if (route.params?.activeTab) {
      setActiveTab(route.params.activeTab);
    }
  }, [route.params?.activeTab]);

  const handleTabChange: Dispatch<SetStateAction<string>> = (value) => {
    if (typeof value === 'function') {
      setActiveTab((prev) => value(prev) as 'questions' | 'communities' | 'news');
    } else {
      setActiveTab(value as 'questions' | 'communities' | 'news');
    }
  };

  return (
    <SafeArea>
      <TopBar />
      <View className="flex-1 mt-4">
        <Tabs tabs={tabs} activeTab={activeTab} onTabChange={handleTabChange} />

        {activeTab === 'questions' && <ForumPostList />}
        {activeTab === 'communities' && <CommunityList />}
        {activeTab === 'news' && <NewsPostList />}
      </View>

      <ForumFloatingButton activeTab={activeTab} />
    </SafeArea>
  );
};

export default ForumScreen;
