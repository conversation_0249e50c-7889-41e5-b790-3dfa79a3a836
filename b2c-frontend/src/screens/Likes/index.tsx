import { View } from 'react-native';
import type { DrawerNavigationProp } from '@react-navigation/drawer';
import { useRoute, useNavigation, type RouteProp } from '@react-navigation/native';
import { useSelector } from 'react-redux';
import BackButton from '@/src/components/BackButton';
import SafeArea from '@/src/components/SafeArea';
import type { ListItem } from '@/src/components/UsersList/types';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { selectCurrentEntityProfile } from '@/src/redux/selectors/entityProfile';
import type {
  BottomTabNavigationI,
  HomeStackParamListI,
  RootDrawerParamListI,
} from '@/src/navigation/types';
import LikesList from './components/LikesList';

const LikesScreen = () => {
  const route = useRoute<RouteProp<HomeStackParamListI, 'Likes'>>();
  const bottomTabNavigation = useNavigation<BottomTabNavigationI>();
  const drawerNavigation = useNavigation<DrawerNavigationProp<RootDrawerParamListI>>();
  const currentUser = useSelector(selectCurrentUser);
  const currentEntityProfile = useSelector(selectCurrentEntityProfile);

  const { postId } = route.params;

  const handleUserPress = (item: ListItem) => {
    const hasEntityProfile = Boolean(item.EntityProfile?.id);
    
    if (hasEntityProfile) {
      // Navigating to an entity profile
      const entityProfileId = item.EntityProfile!.id;
      
      // Check if it's the current user's entity profile
      const isCurrentEntityProfile = currentEntityProfile.entityProfileId === entityProfileId;
      
      if (isCurrentEntityProfile) {
        // Navigate to own entity profile
        bottomTabNavigation.navigate('HomeStack', {
          screen: 'EntityProfile',
          params: { entityProfileId: undefined },
        });
      } else {
        // Navigate to other entity profile
        bottomTabNavigation.navigate('HomeStack', {
          screen: 'EntityProfile',
          params: { entityProfileId },
        });
      }
    } else {
      // Navigating to a user profile
      const profileId = item.Profile.id;
      
      // Navigate to user profile (works for both own and other profiles)
      bottomTabNavigation.navigate('HomeStack', {
        screen: 'OtherUserProfile',
        params: { profileId, fromTabPress: false },
      });
    }
  };

  return (
    <SafeArea>
      <View className="flex-row items-center px-4">
        <BackButton onBack={() => bottomTabNavigation.goBack()} label="" />
      </View>
      <LikesList postId={postId} onUserPress={handleUserPress} />
    </SafeArea>
  );
};

export default LikesScreen;
