type CertificateType = 'STATUTORY' | 'VALUE_ADDED' | 'INSTITUTION' | 'ORGANIZATION' | 'IDENTITY' | 'VISA';

export const SUBTYPE_ROUTE_MAP: Record<string, CertificateType> = {
  statutory: 'STATUTORY',
  'value-added': 'VALUE_ADDED',
  institution: 'INSTITUTION',
  organisation: 'ORGANIZATION',
  identity: 'IDENTITY',
  visa: 'VISA'
} as const;

export const TYPE_COLLECTIONS = new Set([
  'certificate-course',
  'entity',
  'skill',
  'equipmentCategory',
]);
