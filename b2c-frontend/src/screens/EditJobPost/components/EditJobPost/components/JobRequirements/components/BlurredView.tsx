import { View, Text } from "react-native";
import TextView from "@/src/components/TextView";

interface BlurredViewProps {
    hasAdvancedRequirements: boolean;
    hasAdvancedBenefits: boolean;
}

const BlurredView = ({ hasAdvancedRequirements, hasAdvancedBenefits }: BlurredViewProps) => {
    const getAdvancedText = () => {
        if (hasAdvancedRequirements && hasAdvancedBenefits) {
            return "Advanced requirements and benefits";
        } else if (hasAdvancedRequirements) {
            return "Advanced requirements";
        } else {
            return "Advanced benefits";
        }
    };

    return (
        <View className="flex-1 relative">
            <View className="flex-1 opacity-20">
                <TextView subtitle="Step 2/2 : Job Requirements" subtitleClassName="text-[#959090]" className="mb-2"/>
                <View className="gap-2 my-4">
                    <View className="h-20 bg-gray-200 rounded-lg" />
                    <View className="h-20 bg-gray-200 rounded-lg" />
                    <View className="h-20 bg-gray-200 rounded-lg" />
                    <View className="h-20 bg-gray-200 rounded-lg" />
                </View>
            </View>

            <View className="absolute inset-0 flex-1 justify-center items-center bg-white bg-opacity-60">
                <View className="bg-white p-6 rounded-xl mx-4 shadow-lg border border-gray-200">
                    <TextView
                        title="Website Only"
                        titleClassName="text-xl font-bold text-center text-gray-800 mb-2"
                    />
                    <Text className="text-center text-gray-600 leading-6">
                        {getAdvancedText()} are only editable through the website portal.
                        Please use the web application to modify these settings.
                    </Text>
                </View>
            </View>
        </View>
    );
};

export default BlurredView;