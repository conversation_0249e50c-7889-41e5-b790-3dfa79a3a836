import { Platform, Pressable, ScrollView, Text, View } from "react-native"
import TextView from "@/src/components/TextView"
import { Controller } from "react-hook-form"
import { JobRequirementPropsI } from "./types"
import { useJobRequirements } from "./useHook"
import TextInput from "@/src/components/TextInput"
import ToggleSwitch from "@/src/components/Toggle"
import CustomModal from "@/src/components/Modal"
import Button from "@/src/components/Button"
import { useSafeAreaInsets } from "react-native-safe-area-context"
import BlurredView from "./components/BlurredView"

const JobRequirements = ({jobId, editing}:JobRequirementPropsI) => {

    const {
        methods,
        modalVisible,
        handleToggleClick,
        handleModalClose,
        onGeneratePress,
        hasAdvancedRequirements,
        hasAdvancedBenefits,
        isAdvanced
    } = useJobRequirements(jobId, editing)

    const {
        control,
        trigger
    } = methods

    const insets = useSafeAreaInsets()

    if (editing && isAdvanced) {
        return (
            <View className={Platform.OS === 'android' ? `flex-1 pb-${insets.bottom}` : "flex-1"}>
                <BlurredView
                    hasAdvancedRequirements={hasAdvancedRequirements}
                    hasAdvancedBenefits={hasAdvancedBenefits}
                />
            </View>
        );
    }

    return (
        <ScrollView
            contentContainerStyle={{ flexGrow: 1, paddingBottom: 100 }}
            showsVerticalScrollIndicator={false}
        >
        <View className={ Platform.OS === 'android' ? `pb-${insets.bottom}` : ""}>
            <TextView subtitle="Step 2/2 : Job Requirements" subtitleClassName="text-[#959090]" className="mb-2"/>
            <ToggleSwitch
                enabled={false}
                onToggle={handleToggleClick}
                label="Advanced Requirements"
            />
            <View className="gap-2 my-4">
                <Controller
                    control={control}
                    name="about"
                    render={({ field:{ value,onChange}, fieldState: { error } }) => (
                        <TextInput
                            label="About"
                            type='textarea'
                            placeholder="Enter"
                            value={value}
                            onChangeText={(text) => {
                            onChange(text);
                            }}
                            error={error?.message}
                        />
                    )}
                />
                <Controller
                    control={control}
                    name="rolesResponsibilities"
                    render={({ field:{ value,onChange}, fieldState: { error } }) => (
                        <TextInput
                            label="Roles and Responsibilities"
                            type='textarea'
                            placeholder="Enter"
                            value={value}
                            onChangeText={(text) => {
                            onChange(text);
                            }}
                            error={error?.message}
                        />
                    )}
                />
                <Controller
                    control={control}
                    name="requirements"
                    render={({ field:{ value,onChange}, fieldState: { error } }) => (
                        <TextInput
                            label="Requirements"
                            type='textarea'
                            placeholder="Enter"
                            value={value}
                            onChangeText={(text) => {
                            onChange(text);
                            }}
                            error={error?.message}
                        />
                    )}
                />
                <Controller
                    control={control}
                    name="benefits"
                    render={({ field:{ value,onChange}, fieldState: { error } }) => (
                        <TextInput
                            label="Benefits"
                            type='textarea'
                            placeholder="Enter"
                            value={value}
                            onChangeText={(text) => {
                            onChange(text);
                            }}
                            error={error?.message}
                        />
                    )}
                />
            </View>
            <CustomModal
                isVisible={modalVisible}
                onConfirm={handleModalClose}
                title="Can be enabled only on web app"
                confirmText="Ok"
            />
            <Button onPress={onGeneratePress} label={editing ? "Update" : "Generate"}/>
        </View>
        </ScrollView>
    )
}

export default JobRequirements