const validateDate = (value: string | Date): true | string => {
  const inputDate = typeof value === "string" ? new Date(value) : value;
  const today = new Date();

  today.setHours(0, 0, 0, 0);
  inputDate.setHours(0, 0, 0, 0);

  if (isNaN(inputDate.getTime())) {
    return "Invalid date format";
  }

  if (inputDate < today) {
    return "Date cannot be in the past";
  }

  const threeMonthsAfter = new Date(today);
  threeMonthsAfter.setMonth(today.getMonth() + 2);

  if (inputDate > threeMonthsAfter) {
    return "Date cannot be more than 3 months";
  }

  return true;
};

export { validateDate };
