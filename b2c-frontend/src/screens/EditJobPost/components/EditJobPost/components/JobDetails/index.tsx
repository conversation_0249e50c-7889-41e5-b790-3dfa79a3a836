import { Platform, Pressable, Text, View } from "react-native"
import TextView from "@/src/components/TextView"
import EntitySearch from "@/src/components/EntitySearch"
import { useJobDetails } from "./useHook"
import { Controller } from "react-hook-form"
import Select from "@/src/components/Select"
import CustomSlider from "@/src/components/Slider"
import Button from "@/src/components/Button"
import { JobDetailsPropsI } from "./types"
import DatePicker from "@/src/components/DatePicker"
import { useSelector } from "react-redux"
import { selectSelectionByKey } from "@/src/redux/selectors/search"
import { useEffect } from "react"
import TextInput from "@/src/components/TextInput"
import { emailRegex } from "@/src/consts/regEx"
import { useSafeAreaInsets } from "react-native-safe-area-context"
import Checkbox from "@/src/components/Checkbox"
import { ImoDataTypeI } from "@/src/types/common/data"
import { ScrollView } from "react-native-gesture-handler"
import { validateDate } from "./utils"

const JOBTYPE_OPTIONS = [
    { title: 'Sailing', id: 'SAILING' },
    { title: 'Non Sailing', id: 'NON_SAILING' }
];

const APPLICATION_TYPE_OPTIONS = [
    { title: 'In App', id: 'IN_APP' },
    { title: 'Email', id: 'EMAIL' },
    { title: 'Link', id: 'EXTERNAL_LINK' }
];

const JobDetails = ({onNext,jobId,editing}:JobDetailsPropsI) => {
    const {
        methods,
        showShipDetails,
        onGeneratePress,
        clearFields,
        handleToggleShowDetails
    } = useJobDetails(onNext,jobId,editing)
    const {
        control,
        trigger,
        watch
    } = methods
    const entitySelection = useSelector(selectSelectionByKey('entity'));
    const shipSelection = useSelector(selectSelectionByKey('ship'));
    const shipTypeSelection = useSelector(selectSelectionByKey('subVesselType'));
    const designationSelection = useSelector(selectSelectionByKey('designation'));
    const countrySelection = useSelector(selectSelectionByKey('country'));
    
    useEffect(() => {
        if (entitySelection) {
          methods.setValue('entity', entitySelection, { shouldDirty: true });
        }
        if (shipSelection) {
          methods.setValue('ship', shipSelection as unknown as  ImoDataTypeI, { shouldDirty: true });
        }
        if (shipTypeSelection) {
          methods.setValue('shipType', shipTypeSelection, { shouldDirty: true });
        }
        if (designationSelection) {
          methods.setValue('designation', designationSelection, { shouldDirty: true });
        }
        if (countrySelection) {
          methods.setValue('country', countrySelection, { shouldDirty: true });
        }
      }, [ entitySelection, shipSelection, shipTypeSelection, designationSelection, countrySelection, methods, clearFields]);

    const insets = useSafeAreaInsets();
    const expiryDate = watch('expiryDate');
    const applicationType = watch('applicationMethod');
    
    return (
        <ScrollView 
            contentContainerStyle={{ flexGrow: 1, paddingBottom: 140 }}
            showsVerticalScrollIndicator={false}
        >
        <View className={ Platform.OS === 'android' ? `pb-${insets.bottom}` : ""}>
            <TextView subtitle="Step 1/2 : Job details" subtitleClassName="text-[#959090]"/>
            <Controller
                    control={control}
                    name="entity"
                    rules={{ 
                        required: 'Company is required',
                        validate: (value) => value?.id ? true : 'Please select a company'
                    }}
                    render={({ fieldState: { error }, field: { value } }) => (
                        <View>
                            <EntitySearch  
                                selectionKey="entity" 
                                title="Company Name" 
                                placeholder="Select company Name"
                                data={value?.name}
                                error={error?.message}
                            />
                        </View>
                    )}
                />
            <View className="flex-row">
                <View className="flex-1 mr-1">
                    <Controller
                        control={control}
                        name="ship"
                        render={({ fieldState: { error } }) => (
                            <EntitySearch 
                                selectionKey="ship" 
                                title="Ship IMO" 
                                placeholder="Enter"
                                data={shipSelection?.name}
                                error={error?.message}
                            />
                        )}
                    />
                    <Checkbox 
                    label="Display Ship Name"
                    checked={showShipDetails}
                    onValueChange={handleToggleShowDetails}
                />
                </View>
                
                <View className="flex-1 ml-1">
                    <Controller
                        control={control}
                        name="shipType"
                        render={({ fieldState: { error } }) => (
                            <EntitySearch 
                                selectionKey="subVesselType" 
                                title="Ship Type" 
                                placeholder="Enter"
                                data={shipTypeSelection?.name}
                                error={error?.message}
                            />
                        )}
                    />
                </View>
            </View>
            <View className="flex-row">
                <View className="flex-1 mr-1 mt-3">
                    <Controller
                        control={control}
                        name="jobType"
                        rules={{ required: 'Job Type is required' }}
                        render={({ field: { onChange, value, ref }, fieldState: { error } }) => (
                            <Select
                            ref={ref}
                            label="Job Type"
                            labelClassName="font-medium text-sm text-gray-700"
                            options={JOBTYPE_OPTIONS}
                            value={value}
                            onChange={(val) => {
                                onChange(val);
                                trigger('jobType');
                            }}
                            placeholder="Select JobType"
                            error={error?.message}
                            />
                        )}
                    />
                </View>
                <View className="flex-1 ml-1">
                    <Controller
                        control={control}
                        name="designation"
                        rules={{ required: 'Designation is required' }}
                        render={({ fieldState: { error } }) => (
                            <EntitySearch 
                                selectionKey="designation" 
                                title="Designation" 
                                placeholder="Enter"
                                data={designationSelection?.name}
                                error={error?.message}
                            />
                        )}
                    />
                </View>
            </View>
            <Controller
                control={control}
                name="country"
                render={({ fieldState: { error } }) => (
                    <EntitySearch 
                        selectionKey="country" 
                        title="Country" 
                        placeholder="Enter"
                        data={countrySelection?.name}
                        error={error?.message}
                    />
                )}
            />
            <Controller
                control={control}
                name="expiryDate"
                rules={{
                    required: 'ExpiryDate is required',
                    validate: (value) => validateDate(value),
                }}
                render={({ field: { onChange }, fieldState: { error } }) => (
                    <>
                    <DatePicker
                        title="Expires On"
                        selectedDate={expiryDate}
                        onDateChange={(date) => {
                        if (date instanceof Date) {
                            onChange(date.toISOString().split('T')[0]);
                        }
                        }}
                        showMonthYear={true}
                        className={error ? 'border-red-500' : ''}
                    />
                    {error && <Text className="text-red-500 text-xs mt-1">{error.message}</Text>}
                    </>
                )}
            />
            <View className="mx-4">
                <Controller
                    control={control}
                    name="genderEquityIndex"
                    render={({ field: { onChange, value } }) => (
                        <CustomSlider 
                            label="Gender Equity Index"
                            value={value}
                            onChange={onChange}
                        />
                    )}
                />
            </View>
            <View className="mb-4">
                <Controller
                    control={control}
                    name="applicationMethod"
                    rules={{ required: 'Application Method is required' }}
                    render={({ field: { onChange, value, ref }, fieldState: { error } }) => (
                        <Select
                        ref={ref}
                        label="Application Type"
                        labelClassName="font-medium text-sm text-gray-700"
                        options={APPLICATION_TYPE_OPTIONS}
                        value={value}
                        onChange={(val) => {
                            onChange(val);
                            trigger('applicationMethod');
                            trigger('applicationEmail');
                            trigger('applicationLink');
                        }}
                        placeholder="Select Application type"
                        error={error?.message}
                        />
                    )}
                />
            </View>
            
            {applicationType?.toLowerCase() === 'email' && (
                <View className="my-4">
                    <Controller
                        control={control}
                        name="applicationEmail"
                        rules={{ 
                            required: 'Email is required',
                            pattern: {
                                value: emailRegex,
                                message: 'Please enter a valid email address'
                            }
                        }}
                        render={({ field: { onChange, value, ref }, fieldState: { error } }) => (
                            <TextInput
                                label="Email"
                                placeholder="Enter email"
                                value={value}
                                onChangeText={(text) => {
                                    onChange(text);
                                    trigger('applicationEmail');
                                }}
                                error={error?.message}
                                keyboardType="email-address"
                                autoCapitalize="none"
                            />
                        )}
                    />
                </View>
            )}
            
            {applicationType?.toLowerCase() === 'link' && (
                <View className="my-4">
                    <Controller
                        control={control}
                        name="applicationLink"
                        rules={{ 
                            required: 'Link is required',
                            validate: {
                                validUrl: (value) => {
                                    if (!value) return 'Link is required';
                                    try {
                                        new URL(value);
                                        return true;
                                    } catch {
                                        return 'Please enter a valid URL';
                                    }
                                }
                            }
                        }}                    
                        render={({ field: { onChange, value, ref }, fieldState: { error } }) => (
                            <TextInput
                                label="Application Link"
                                placeholder="Enter application link "
                                value={value}
                                onChangeText={(text) => {
                                    onChange(text);
                                    trigger('applicationLink');
                                }}
                                error={error?.message}
                                keyboardType="url"
                                autoCapitalize="none"
                            />
                        )}
                    />
                </View>
            )}
            
            <Button onPress={onGeneratePress} label={editing ? "Update" : "Generate"}/>
        </View>
        </ScrollView>
    )
}

export default JobDetails