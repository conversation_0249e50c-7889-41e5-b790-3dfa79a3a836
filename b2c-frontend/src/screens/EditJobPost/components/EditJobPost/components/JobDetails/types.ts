import { SearchResultI } from "@/src/redux/slices/entitysearch/types";
import { ImoDataTypeI } from "@/src/types/common/data";

export type JobDetailsPropsI = {
    onNext: (id:string) => void;
    jobId?:string;
    editing?: boolean;
}

export type JobDetailsFormDataI = {
    entity: SearchResultI;
    ship: ImoDataTypeI;
    shipType: SearchResultI;
    jobType: string;
    designation : SearchResultI;
    genderEquityIndex: number
    expiryDate: '';
    applicationMethod:string;
    applicationEmail?:string;
    applicationLink?:string;
    country:SearchResultI
}