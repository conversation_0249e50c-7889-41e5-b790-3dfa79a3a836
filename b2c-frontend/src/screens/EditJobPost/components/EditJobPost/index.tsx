import { EditJobPostPropsI } from "./types"
import { useEditJobPost } from "./useHook"
import { Pressable, ScrollView, View } from "react-native";
import JobDetails from "./components/JobDetails";
import JobRequirements from "./components/JobRequirements";
import BackButton from "@/src/components/BackButton";
import TextView from "@/src/components/TextView";
import BottomSheet from "@/src/components/Bottomsheet";
import { OptionItem, OptionsMenu } from "@/src/components/OptionsMenu";

const EditJobPost = ({ jobId, editing }:EditJobPostPropsI) => {
    const {
        currentStepIndex,
        bottomSheetVisible,
        createdJobId,
        onNext,
        handleBack,
        handleEmailLinkJobCreation,
        onCloseBottomSheet,
        handleEmailPress,
        handleLinkPress
    } = useEditJobPost(jobId, editing);

    return(
         <ScrollView 
            contentContainerStyle={{ flexGrow: 1, paddingHorizontal: 16 }}
            showsVerticalScrollIndicator={false}
        >
            <BackButton label={editing ? "Edit job" : "Post new job"} onBack={handleBack} />
            {(() => {
              switch (currentStepIndex) {
                case 0:
                  return (
                    <JobDetails
                        onNext={onNext}
                        jobId={jobId}
                        editing={editing}
                    />
                  );
                case 1:
                  return (
                    <JobRequirements
                      jobId={createdJobId}
                      editing={editing}
                    />
                  );
                default:
                  return (
                    <JobDetails
                        onNext={onNext}
                        jobId={jobId}
                        editing={editing}
                    />
                  );
              }
            })()}
            <BottomSheet 
                visible={bottomSheetVisible}
                onClose={onCloseBottomSheet}
                onModalHide={onCloseBottomSheet}
            >
                <OptionsMenu>
                    <OptionItem label='Post Job with email' onPress={handleEmailPress}/>
                    <OptionItem label='Post Job with link' onPress={handleLinkPress}/>
                </OptionsMenu>
            </BottomSheet>
          </ScrollView>
    )
}

export default EditJobPost