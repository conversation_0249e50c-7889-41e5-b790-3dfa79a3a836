import { CareerStackParamListI } from "@/src/navigation/types";
import { useNavigation } from "@react-navigation/native";
import { StackNavigationProp } from "@react-navigation/stack";
import { useState, useEffect } from "react";

export const useEditJobPost = (jobId?:string, editing?: boolean) => {
    const navigation = useNavigation<StackNavigationProp<CareerStackParamListI>>();
    const [currentStepIndex,setCurrentStepIndex] = useState(0);
    const [bottomSheetVisible,setBottomSheetVisible] = useState(false);
    const [createdJobId, setCreatedJobId] = useState<string | null>(null);

    useEffect(() => {
        if (!editing) {
            setCurrentStepIndex(0);
            setCreatedJobId(null);
        }
    }, [editing]);

    const handleBack = () => {
        if(currentStepIndex === 0){
            navigation.goBack();
        }else{
            setCurrentStepIndex(0)
        }
        
    }
    const handleEmailLinkJobCreation = () => {
        setBottomSheetVisible(true)
    }
    const onCloseBottomSheet = () => {
        setBottomSheetVisible(false)
    }
    const handleEmailPress = () => {
        navigation.navigate('CreateExternalJobPost',{
            type:'email'
        })
    }
    const handleLinkPress = () => {
        navigation.navigate('CreateExternalJobPost',{
            type:'link'
        })
    }

    const onNext = (id:string) => {
        setCreatedJobId(id)
        setCurrentStepIndex(1)
    }

    return {
        currentStepIndex,
        bottomSheetVisible,
        onNext,
        handleBack,
        handleEmailLinkJobCreation,
        onCloseBottomSheet,
        handleEmailPress,
        handleLinkPress,
        createdJobId: createdJobId || jobId
    }
}