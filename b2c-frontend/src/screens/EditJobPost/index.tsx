import { RouteProp, useRoute } from "@react-navigation/native"
import { CareerStackParamListI } from "@/src/navigation/types";
import EditJobPost from "./components/EditJobPost";
import SafeArea from "@/src/components/SafeArea";
import { ScrollView } from "react-native-gesture-handler";
import { View } from "react-native";

const EditJobPostScreen = () => {
    const route = useRoute<RouteProp<CareerStackParamListI, 'EditJobPost'>>();
    const { jobId, editing } = route.params;

    return(
        <SafeArea>
            <View className="flex-1">
                <EditJobPost
                    jobId={jobId}
                    editing={editing}
                />
            </View>
        </SafeArea>
    )
}

export default EditJobPostScreen