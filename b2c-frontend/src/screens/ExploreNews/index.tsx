import React, { useCallback, useEffect, useState } from 'react';
import {
  ActivityIndicator,
  RefreshControl,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
  Linking,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { StackNavigationProp, StackScreenProps } from '@react-navigation/stack';
import BackButton from '@/src/components/BackButton';
import NotFound from '@/src/components/NotFound';
import SafeArea from '@/src/components/SafeArea';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';
import NewsPost from '@/src/screens/News/components/NewsPost';
import NewsPostSkeleton from '@/src/screens/News/components/NewsPostSkeleton';
import { fetchNewsAPI } from '@/src/networks/news/news';
import type { NewsItemI, NewsFetchManyPayloadI } from '@/src/networks/news/types';

const POSTS_PER_PAGE = 10;

const ExploreNewsScreen: React.FC = () => {
  const route = useRoute<StackScreenProps<LearnCollabStackParamsListI, 'ExploreNews'>['route']>();
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();

  // Local state for news data - no Redux interference
  const [newsItems, setNewsItems] = useState<NewsItemI[]>([]);
  const [nextCursorId, setNextCursorId] = useState<string | null>(null);
  const [hasMorePages, setHasMorePages] = useState(true);

  // Loading states
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const buildNewsQueryParams = useCallback(
    (cursorId: string | null, shouldResetCursor: boolean): NewsFetchManyPayloadI => {
      const routeParams = route.params;
      const effectiveCursorId = shouldResetCursor ? null : cursorId;

      return {
        pageSize: POSTS_PER_PAGE.toString(),
        ...(effectiveCursorId && { cursorId: effectiveCursorId }), // Only include cursorId if it's not null
        ...(routeParams?.topicId && { topicId: routeParams.topicId }),
        sortBy: 'publishedDate',
        sortOrder: 'desc',
      };
    },
    [route.params],
  );

  const loadNewsItems = useCallback(
    async (shouldRefresh: boolean) => {
      try {
        setErrorMessage(null);

        if (shouldRefresh) {
          setIsRefreshing(true);
        } else if (newsItems.length === 0) {
          setIsInitialLoading(true);
        } else {
          setIsLoadingMore(true);
        }

        const queryParams = buildNewsQueryParams(nextCursorId, shouldRefresh);
        const apiResponse = await fetchNewsAPI(queryParams);

        if (shouldRefresh) {
          // Replace all news on refresh
          setNewsItems(apiResponse.data);
        } else {
          // Append new news and deduplicate
          const existingNewsIds = new Set(newsItems.map((item) => item.id));
          const newUniqueNews = apiResponse.data.filter((item) => !existingNewsIds.has(item.id));
          setNewsItems((prevNews) => [...prevNews, ...newUniqueNews]);
        }

        // Update pagination state
        setNextCursorId(apiResponse.nextCursorId);

        // Calculate if there are more pages available
        const totalLoadedNews = shouldRefresh
          ? apiResponse.data.length
          : newsItems.length + apiResponse.data.length;

        const hasMoreData =
          totalLoadedNews < apiResponse.total && Boolean(apiResponse.nextCursorId);
        setHasMorePages(hasMoreData);
      } catch (error) {
        console.error('Failed to load news:', error);
        setErrorMessage('Failed to load news articles. Please try again.');
      } finally {
        setIsLoadingMore(false);
        setIsRefreshing(false);
        setIsInitialLoading(false);
      }
    },
    [buildNewsQueryParams, nextCursorId, newsItems],
  );

  // Reset state and load initial data when route params change
  useEffect(() => {
    setNewsItems([]);
    setNextCursorId(null);
    setHasMorePages(true);
    setErrorMessage(null);
    loadNewsItems(true);
  }, [route.params]);

  const handleRefresh = useCallback(() => {
    loadNewsItems(true);
  }, [loadNewsItems]);

  const handleLoadMore = useCallback(() => {
    if (!isLoadingMore && hasMorePages && !errorMessage) {
      loadNewsItems(false);
    }
  }, [isLoadingMore, hasMorePages, errorMessage, loadNewsItems]);

  const handleRetry = useCallback(() => {
    if (newsItems.length === 0) {
      loadNewsItems(true);
    } else {
      loadNewsItems(false);
    }
  }, [newsItems.length, loadNewsItems]);

  const handleNewsPress = useCallback(async (url: string) => {
    try {
      const supported = await Linking.canOpenURL(url);
      if (supported) {
        await Linking.openURL(url);
      } else {
        console.log("Don't know how to open URI: " + url);
      }
    } catch (error) {
      console.error('Error opening URL:', error);
    }
  }, []);

  const handleSelectNews = useCallback(
    (newsItem: NewsItemI) => {
      handleNewsPress(newsItem.link);
    },
    [handleNewsPress],
  );

  const renderLoadingFooter = useCallback(() => {
    if (!isLoadingMore) return null;

    return (
      <View className="py-4 items-center">
        <ActivityIndicator size="small" color="#666" />
      </View>
    );
  }, [isLoadingMore]);

  const renderErrorMessage = useCallback(() => {
    if (!errorMessage || isInitialLoading) return null;

    return (
      <View className="py-4 px-4 items-center">
        <Text className="text-red-500 text-center mb-2">{errorMessage}</Text>
        <TouchableOpacity onPress={handleRetry} className="bg-blue-500 px-4 py-2 rounded">
          <Text className="text-white font-medium">Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }, [errorMessage, isInitialLoading, handleRetry]);

  const renderInitialLoadingSkeleton = useCallback(
    () => (
      <View className="flex-1">
        {Array.from({ length: 5 }).map((_, index) => (
          <NewsPostSkeleton key={`news-skeleton-${index}`} />
        ))}
      </View>
    ),
    [],
  );

  if (isInitialLoading && newsItems.length === 0) {
    return (
      <SafeArea>
        <BackButton onBack={() => navigation.goBack()} label="Explore News" />
        <View className="flex-1 bg-white">{renderInitialLoadingSkeleton()}</View>
      </SafeArea>
    );
  }

  return (
    <SafeArea>
      <BackButton
        onBack={() => navigation.goBack()}
        label="Explore News"
        labelClassname="font-semibold text-2xl"
      />
      <View className="flex-1 bg-white">
        <ScrollView
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl refreshing={isRefreshing} onRefresh={handleRefresh} tintColor="#666" />
          }
        >
          {/* News Articles Section */}
          <View className="p-2">
            {newsItems.length > 0 ? (
              newsItems.map((newsItem) => (
                <NewsPost
                  key={newsItem.id}
                  news={{
                    id: newsItem.id,
                    title: newsItem.title,
                    link: newsItem.link,
                    publishedDate: newsItem.publishedDate,
                    source: newsItem.provider.name,
                    scrapedAt: newsItem.scrapedAt,
                    topics: newsItem.topics,
                    onPress: () => handleSelectNews(newsItem),
                  }}
                />
              ))
            ) : !isInitialLoading && !errorMessage ? (
              <NotFound
                title="No news articles found"
                subtitle="Try exploring a different topic"
                fullScreen={false}
                className="py-4"
                titleClassName="text-base font-medium"
              />
            ) : null}

            {renderLoadingFooter()}
          </View>

          {/* Load More Button */}
          {hasMorePages && !isLoadingMore && newsItems.length > 0 && (
            <View className="px-4 pb-4">
              <TouchableOpacity
                onPress={handleLoadMore}
                className="bg-primaryGreen py-3 px-6 rounded-full items-center"
              >
                <Text className="text-white font-medium">Load More</Text>
              </TouchableOpacity>
            </View>
          )}
        </ScrollView>
        {renderErrorMessage()}
      </View>
    </SafeArea>
  );
};

export default ExploreNewsScreen;
