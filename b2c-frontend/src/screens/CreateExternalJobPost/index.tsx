import BackButton from "@/src/components/BackButton"
import SafeArea from "@/src/components/SafeArea"
import { CareerStackParamListI } from "@/src/navigation/types";
import { capitalizeFirstLetter } from "@/src/utilities/data/string";
import { RouteProp, useNavigation, useRoute } from "@react-navigation/native";
import { StackNavigationProp } from "@react-navigation/stack";
import { View } from "react-native"
import CreateExternalJobPost from "./components/CreateExternalJobPost";

const CreateExternalJobPostScreen = () => {
    const navigation = useNavigation<StackNavigationProp<CareerStackParamListI>>();
    const route = useRoute<RouteProp<CareerStackParamListI, 'CreateExternalJobPost'>>()
    // const { type } = route.params;
    const type = 'email';

    const handleBack = () => {
        navigation.goBack()
    }
    return(
        <SafeArea>
            <View className="px-4">
                <BackButton label={`Post Job With ${capitalizeFirstLetter(type)}`} onBack={handleBack}/>
                <CreateExternalJobPost 
                    type={type}
                />
            </View>
        </SafeArea>
    )
}

export default CreateExternalJobPostScreen