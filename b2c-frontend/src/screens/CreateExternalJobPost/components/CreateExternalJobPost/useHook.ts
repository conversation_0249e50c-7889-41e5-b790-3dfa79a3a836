import { useForm } from "react-hook-form";
import { CreateExternalJobPostFormDataI } from "./types";

export const useCreateExternalJobPost = () => {
    const methods = useForm<CreateExternalJobPostFormDataI>({
        mode: 'onChange',
        defaultValues: {
          email: '',
          link: '',
        },
      });

    const onGeneratePress = () => {

    }

    return {
        methods,
        onGeneratePress
    }
}