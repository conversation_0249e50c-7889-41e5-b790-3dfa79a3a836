import { View } from "react-native"
import { CreateExternalJobPostProps } from "./types"
import { useCreateExternalJobPost } from "./useHook"
import { Controller } from "react-hook-form"
import Button from "@/src/components/Button"
import TextInput from "@/src/components/TextInput"

const CreateExternalJobPost = ({type}:CreateExternalJobPostProps) => {
    const {
        methods,
        onGeneratePress
    } = useCreateExternalJobPost()
    const {
        control,
        trigger
    } = methods
    return (
        <View className="gap-3">
        {
            type === 'email' ?
            <Controller
                control={control}
                name="email"
                rules={{ required: 'Email is required' }}
                render={({ field: { onChange }, fieldState: { error } }) => (
                    <TextInput 
                        label="Email ID" 
                        placeholder="Enter Email ID"
                        onChange={onChange}
                        error={error?.message}
                    />
                )}
            />
            :
            <Controller
                control={control}
                name="link"
                rules={{ required: 'Job Link is required' }}
                render={({ field: { onChange }, fieldState: { error } }) => (
                    <TextInput 
                        label="Link" 
                        placeholder="Enter Link"
                        onChange={onChange}
                        error={error?.message}
                    />
                )}
            />
        }
        <Button onPress={onGeneratePress} label="Submit"/>
        </View>
    )
}

export default CreateExternalJobPost