import { useState, useCallback, useRef } from 'react';
import { useFocusEffect } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import {
  fetchSavedPosts,
  addReactionOptimistc,
  removeReactionOptimistic,
  deletePostOptimistic,
  revertDeletePostOptimistic,
} from '@/src/redux/slices/content/contentSlice';
import { AppDispatch, RootState } from '@/src/redux/store';
import { showToast } from '@/src/utilities/toast';
import APIResError from '@/src/errors/networks/APIResError';
import { deletePostAPI } from '@/src/networks/content/post';
import { upsertReactionAPI, deleteReactionAPI } from '@/src/networks/content/reaction';
import { UseSavedPostsReturn } from './types';

export const useSavedPosts = (): UseSavedPostsReturn => {
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const dispatch = useDispatch<AppDispatch>();
  const { savedPosts, savedPostsPagination } = useSelector((state: RootState) => state.content);
  const currentUser = useSelector(selectCurrentUser);

  const lastSeenCursorId = useRef<string | null>(null);
  const firstRender = useRef(true);

  if (error) throw error;

  const fetchLatestSavedPosts = async () => {
    setLoading(true);
    try {
      const currentCursor = savedPostsPagination.cursorId ?? null;
      const shouldRefresh = firstRender.current && lastSeenCursorId.current !== currentCursor;
      await dispatch(
        fetchSavedPosts(shouldRefresh ? { refresh: true, pageSize: '10' } : { pageSize: '10' }),
      ).unwrap();
      if (shouldRefresh) {
        lastSeenCursorId.current = currentCursor;
        firstRender.current = false;
      }
    } catch (err) {
      if (!savedPosts.length)
        setError(
          new Error(
            `Failed to fetch saved posts: ${err instanceof APIResError ? err.message : 'Unknown error'}`,
          ),
        );
      else showToast({ message: 'Failed to refresh saved posts', type: 'error' });
    } finally {
      setLoading(false);
    }
  };

  useFocusEffect(
    useCallback(() => {
      fetchLatestSavedPosts();
    }, []),
  );

  const handleRefresh = async () => {
    if (refreshing) return;
    setRefreshing(true);
    try {
      await dispatch(fetchSavedPosts({ refresh: true, pageSize: '10' })).unwrap();
      lastSeenCursorId.current = savedPostsPagination.cursorId ?? null;
    } catch {
      if (savedPosts.length) showToast({ message: 'Failed to refresh saved posts', type: 'error' });
    } finally {
      setRefreshing(false);
    }
  };

  const handleLoadMore = async () => {
    if (!savedPostsPagination.hasMore || loading || refreshing || savedPosts.length === 0) return;
    setLoading(true);
    try {
      await dispatch(fetchSavedPosts({ pageSize: '10' })).unwrap();
    } catch {
      showToast({ message: 'Failed to load more saved posts', type: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const handleLikePost = async (postId: string) => {
    const post = savedPosts.find((p) => p.id === postId);
    if (!post || !currentUser) return;
    try {
      if (post.isLiked) {
        dispatch(removeReactionOptimistic({ postId }));
        await deleteReactionAPI({ postId });
      } else {
        dispatch(addReactionOptimistc({ postId }));
        await upsertReactionAPI({ postId, reactionType: 'LIKE' });
      }
    } catch {
      if (post.isLiked) dispatch(addReactionOptimistc({ postId }));
      else dispatch(removeReactionOptimistic({ postId }));
      showToast({ message: 'Failed to update like', type: 'error' });
    }
  };

  const handleDeletePost = async (postId: string) => {
    const post = savedPosts.find((p) => p.id === postId);
    if (!post) return;
    try {
      dispatch(deletePostOptimistic({ post }));
      await deletePostAPI(postId);
    } catch {
      dispatch(revertDeletePostOptimistic({ postId }));
      showToast({ message: 'Failed to delete post', type: 'error' });
    }
  };

  return {
    savedPosts,
    loading,
    refreshing,
    hasMore: savedPostsPagination.hasMore,
    handleRefresh,
    handleLoadMore,
    handleLikePost,
    handleDeletePost,
  };
};
