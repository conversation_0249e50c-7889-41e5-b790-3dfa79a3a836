import React, { useRef, useState } from 'react';
import { View, ActivityIndicator, RefreshControl, FlatList } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import BackButton from '@/src/components/BackButton';
import NotFound from '@/src/components/NotFound';
import SafeArea from '@/src/components/SafeArea';
import UserPost from '@/src/components/UserPost';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { savePostOptimistic, unsavePostOptimistic } from '@/src/redux/slices/content/contentSlice';
import { AppDispatch } from '@/src/redux/store';
import { showToast } from '@/src/utilities/toast';
import type { BottomTabNavigationI } from '@/src/navigation/types';
import { savePostAPI, unsavePostAPI } from '@/src/networks/content/post';
import { PostExternalClientI } from '@/src/networks/content/types';
import PostListSkeleton from '../Home/components/PostListSkeleton';
import { useSavedPosts } from './useHook';

const INITIAL_NUM_TO_RENDER = 5;
const END_REACHED_THRESHOLD = 0.5;
const WINDOW_SIZE = 5;

const ListFooter = ({ isLoading }: { isLoading: boolean }) => {
  if (!isLoading) return null;
  return (
    <View className="py-4">
      <ActivityIndicator size="small" color="#10B981" />
    </View>
  );
};

const SavedPosts: React.FC = () => {
  const {
    savedPosts,
    loading,
    refreshing,
    hasMore,
    handleRefresh,
    handleLoadMore,
    handleLikePost,
    handleDeletePost,
  } = useSavedPosts();
  const currentUser = useSelector(selectCurrentUser);
  const flatListRef = useRef<FlatList>(null);
  const [firstLoading, setFirstLoading] = useState(false);
  const navigationObj = useNavigation<BottomTabNavigationI>();
  const dispatch = useDispatch<AppDispatch>();

  if (loading && !firstLoading) setFirstLoading(true);

  const handleGoBack = () => navigationObj.goBack();

  const handleEndReached = () => {
    if (!loading && !refreshing && hasMore) handleLoadMore();
  };

  const handleSavePost = async (postId: string) => {
    dispatch(savePostOptimistic({ postId }));
    try {
      await savePostAPI(postId);
      showToast({ type: 'success', message: 'Post saved successfully' });
    } catch (error) {
      dispatch(unsavePostOptimistic({ postId }));
      showToast({ type: 'error', message: 'Failed to save post' });
    }
  };

  const handleUnsavePost = async (postId: string) => {
    dispatch(unsavePostOptimistic({ postId }));
    try {
      await unsavePostAPI(postId);
      showToast({ type: 'success', message: 'Post removed from saved posts' });
    } catch (error) {
      dispatch(savePostOptimistic({ postId }));
      showToast({ type: 'error', message: 'Failed to unsave post' });
    }
  };

  const renderItem = ({ item, index }: { item: PostExternalClientI; index: number }) => (
    <UserPost
      post={item}
      onLikePress={() => handleLikePost(item.id)}
      onDeletePress={() => handleDeletePost(item.id)}
      onSavePress={() => handleSavePost(item.id)}
      onUnsavePress={() => handleUnsavePost(item.id)}
      isOwnPost={currentUser?.profileId === item.Profile?.id}
      onLikeCountPress={() =>
        navigationObj.navigate('HomeStack', {
          screen: 'Likes',
          params: { postId: item.id, type: 'USER_POST' },
        })
      }
      onEditPress={() =>
        navigationObj.navigate('CreateStack', {
          screen: 'CreateContent',
          params: { postId: item.id, editing: true, type: 'USER_POST' },
        })
      }
      onCommentPress={() =>
        navigationObj.navigate('HomeStack', {
          screen: 'Comment',
          params: { postId: item.id, type: 'USER_POST' },
        })
      }
      parentScrollRef={flatListRef}
      postIndex={index}
    />
  );

  const keyExtractor = (item: PostExternalClientI) => item.id;

  if ((loading && !savedPosts?.length) || !firstLoading) {
    return (
      <SafeArea>
        <View className="flex-1 bg-white">
          <View className="px-4">
            <BackButton onBack={handleGoBack} label="Saved Posts" />
          </View>
          <PostListSkeleton />
        </View>
      </SafeArea>
    );
  }

  if (!savedPosts?.length && firstLoading) {
    return (
      <SafeArea>
        <View className="flex-1 bg-white">
          <View className="px-4">
            <BackButton onBack={handleGoBack} label="Saved Posts" />
          </View>
          <NotFound title="No saved posts" subtitle="Posts you save will appear here!" />
        </View>
      </SafeArea>
    );
  }

  return (
    <SafeArea>
      <View className="flex-1">
        <BackButton onBack={handleGoBack} label="Saved Posts" />
        <View className="flex-1">
          <FlatList
            ref={flatListRef}
            data={savedPosts}
            renderItem={renderItem}
            keyExtractor={keyExtractor}
            showsVerticalScrollIndicator={false}
            initialNumToRender={INITIAL_NUM_TO_RENDER}
            maxToRenderPerBatch={INITIAL_NUM_TO_RENDER}
            windowSize={WINDOW_SIZE}
            removeClippedSubviews
            refreshControl={
              <RefreshControl
                enabled
                refreshing={refreshing}
                onRefresh={handleRefresh}
                tintColor="#10B981"
                colors={['#10B981']}
              />
            }
            contentContainerStyle={{ flexGrow: 1, backgroundColor: '#F9FAFB', paddingBottom: 20 }}
            onEndReached={handleEndReached}
            onEndReachedThreshold={END_REACHED_THRESHOLD}
            ListFooterComponent={<ListFooter isLoading={loading && hasMore} />}
          />
        </View>
      </View>
    </SafeArea>
  );
};

export default SavedPosts;
