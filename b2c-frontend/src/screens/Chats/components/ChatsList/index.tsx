import { useState, useEffect } from 'react';
import { FlatList, View, ActivityIndicator, Text } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { HomeStackParamListI } from '@/src/navigation/types';
import AiBot from '@/src/assets/images/others/aibot.png';
import ChatItem from '../ChatItem';
import { useChatsListHook } from './useHook';

const ChatsList = () => {
  const navigation = useNavigation<NativeStackNavigationProp<HomeStackParamListI>>();
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const {
    chats,
    loading,
    error,
    refreshing,
    loadingMore,
    handleRefresh,
    handleLoadMore,
    removeChat,
  } = useChatsListHook();

  useEffect(() => {
    if (!loading) {
      const timer = setTimeout(() => {
        setIsInitialLoading(false);
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [loading]);

  const renderFooter = () => {
    if (!loadingMore) return null;
    return (
      <View className="py-4 items-center">
        <ActivityIndicator size="small" color="#448600" />
      </View>
    );
  };

  if (isInitialLoading || (loading && !refreshing && !loadingMore)) {
    return (
      <View className="flex-1 bg-white items-center justify-center">
        <ActivityIndicator size="small" color="#448600" />
        <Text className="text-gray-500 mt-2">Loading chats...</Text>
      </View>
    );
  }

  if (error && !refreshing && !loadingMore) {
    return (
      <View className="flex-1 bg-white items-center justify-center">
        <Text className="text-red-500 text-center px-4">{error}</Text>
      </View>
    );
  }

  return (
    <View className="flex-1 bg-white">
      <FlatList
        data={chats}
        keyExtractor={(item) => item.id.$oid}
        renderItem={({ item }) => <ChatItem item={item} onDelete={removeChat} />}
        ItemSeparatorComponent={() => <View className="h-px bg-borderGrayLight" />}
        contentContainerStyle={{
          flexGrow: 1,
          backgroundColor: 'white',
          paddingBottom: 12,
        }}
        refreshing={refreshing}
        onRefresh={handleRefresh}
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.1}
        ListFooterComponent={renderFooter}
        ListEmptyComponent={
          <View className="flex-1 items-center justify-center py-20">
            <Text className="text-gray-500 text-center">No chats yet</Text>
            <Text className="text-gray-400 text-center mt-1">Start a conversation!</Text>
          </View>
        }
      />
    </View>
  );
};

export default ChatsList;
