import { useNavigation } from '@react-navigation/native';
import SafeArea from '@/src/components/SafeArea';
import Careers from './components/Careers';
import { StackNavigationProp } from '@react-navigation/stack';
import { CareerStackParamListI } from '@/src/navigation/types';
import FloatingActionButton from '@/src/components/FloatingActionButton';

const CareersScreen = () => {
  const navigation = useNavigation<StackNavigationProp<CareerStackParamListI>>();

  const handleCreateJobPost = () => {
    navigation.navigate('EditJobPost',{
      jobId:undefined
    })
  }

  return (
    <SafeArea>
      <Careers onBack={navigation.goBack} />
      <FloatingActionButton
        context="CREATE_JOB_POST"
        onPress={handleCreateJobPost}
        visible={true}
      />
    </SafeArea>
  );
};

export default CareersScreen;
