import { SetStateAction, useCallback, useState } from 'react';
import { ActivityIndicator, FlatList, RefreshControl, Text, View } from 'react-native';
import BackButton from '@/src/components/BackButton';
import CareerFilterBar from '@/src/components/CareerFilterBar';
import Job from '@/src/components/Job';
import SafeArea from '@/src/components/SafeArea';
import Tabs from '@/src/components/Tabs';
import { useMyJobs } from './useHook';

const MyJobsScreen = () => {
  const [activeTab, setActiveTab] = useState('applied');

  const tabs = [
    { id: 'applied', label: 'Applied' },
    { id: 'shortlisted', label: 'Shortlisted' },
    { id: 'offered', label: 'Offered' },
  ];

  const {
    jobs,
    searchText,
    navigation,
    filterTabs,
    isLoadingMore,
    loading,
    loadingFilters,
    setSearchText,
    loadMoreJobs,
    fetchJobs,
    onFilterPress,
  } = useMyJobs(activeTab);

  const onRefresh = useCallback(() => {
    fetchJobs(false,true);
  }, []);

  const renderFooter = () => (
    <View className="pb-4">
      {isLoadingMore && <ActivityIndicator size="small" className="my-4" />}
    </View>
  );

  return (
    <SafeArea>
      <BackButton label="My Jobs" onBack={navigation.goBack} />
      <CareerFilterBar
        page="myJobs"
        ellipsesVisible={false}
        onSearchTextChange={setSearchText}
        searchTextValue={searchText}
        className="pb-5"
        filterTabs={filterTabs}
        onFilterPress={onFilterPress}
        onApplyPress={() => fetchJobs(false, true)}
        loading={loadingFilters}
      />
      <Tabs
        tabs={tabs}
        activeTab={activeTab}
        onTabChange={setActiveTab as React.Dispatch<SetStateAction<string>>}
      />
      <FlatList
        data={jobs}
        renderItem={({ item }) => <Job job={item} />}
        keyExtractor={(item) => item.id}
        showsVerticalScrollIndicator={false}
        onEndReached={loadMoreJobs}
        onEndReachedThreshold={0.5}
        ListFooterComponent={renderFooter}
        refreshControl={
          <RefreshControl refreshing={loading} onRefresh={onRefresh} tintColor="#000" />
        }
      />
    </SafeArea>
  );
};

export default MyJobsScreen;
