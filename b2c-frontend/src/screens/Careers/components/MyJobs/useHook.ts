import { useCallback, useEffect, useState } from 'react';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useDispatch, useSelector } from 'react-redux';
import { IdLabelAppliedCountI } from '@/src/components/CareerFilterBar/types';
import { selectActiveFilterNumbers, selectActiveFilters } from '@/src/redux/selectors/careers';
import { setFilters } from '@/src/redux/slices/career/careerSlice';
import { AppDispatch } from '@/src/redux/store';
import { CareerStackParamListI } from '@/src/navigation/types';
import { fetchFiltersForApplicantAPI } from '@/src/networks/jobs/fetchFiltersForApplicants';
import { fetchJobsForApplicantAPI } from '@/src/networks/jobs/fetchJobsForApplicants';
import { JobI } from '../Careers/types';
import { showToast } from '@/src/utilities/toast';

const PAGE_SIZE = 10;

export const useMyJobs = (type: string) => {
  const navigation = useNavigation<StackNavigationProp<CareerStackParamListI>>();
  const dispatch = useDispatch<AppDispatch>();
  const [loading, setLoading] = useState(false);
  const [loadingFilters,setLoadingFilters] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [searchText, setSearchText] = useState('');
  const [cursorId, setCursorId] = useState<string | null>(null);
  const activeFilterNumbers = useSelector(selectActiveFilterNumbers('myJobs'));
  const [jobs, setJobs] = useState<JobI[]>([]);
  const filters = useSelector(selectActiveFilters('myJobs'));

  const filterTabs: IdLabelAppliedCountI[] = [
    {
      id: 'location',
      label: 'Location',
      appliedCount: activeFilterNumbers.locations || 0,
    },
    {
      id: 'designation',
      label: 'Designation',
      appliedCount: activeFilterNumbers.designations || 0,
    },
    {
      id: 'internet',
      label: 'Internet',
      appliedCount: activeFilterNumbers.internetLimits || 0,
    },
    {
      id: 'ship-type',
      label: 'Ship Type',
      appliedCount: activeFilterNumbers.shipTypes || 0,
    },
  ];

  const fetchJobs = async (loadMore = false, reset = false) => {
    try {
      if (reset) {
        setCursorId(null);
        setJobs([]);
        setLoading(true);
      } else if (loadMore) {
        if (!cursorId || !hasMore) return;
        setIsLoadingMore(true);
      } else {
        setLoading(true);
      }

      const query = {
        cursorId: reset ? null : cursorId,
        pageSize: PAGE_SIZE,
        status: transformStatus(type),
      };
      const body = {
        designations: filters.designations,
        shipTypes: filters.shipTypes,
        countries: filters.locations,
        internetLimits: filters.internetLimits
      };
      
      const result = await fetchJobsForApplicantAPI(query, body);
      if (loadMore) {
        setJobs((prev) => [...prev, ...result.data]);
      } else {
        setJobs(result.data);
      }

      setCursorId(result.nextCursorId);
      setHasMore(result.nextCursorId !== null);
    } catch (error) {
    } finally {
      if (loadMore) {
        setIsLoadingMore(false);
      } else {
        setLoading(false);
      }
    }
  };

  useEffect(() => {
    const fetchInitialJobs = async () => {
      try {
        setLoading(true);
        const query = {
          cursorId: null,
          pageSize: PAGE_SIZE,
          status: transformStatus(type),
        };
        const body = {
          designations: filters.designations,
          shipTypes: filters.shipTypes,
          countries: filters.locations,
          internetLimits: filters.internetLimits
        };
        const result = await fetchJobsForApplicantAPI(query, body);
        setJobs(result.data);

        setCursorId(result.nextCursorId);
        setHasMore(result.nextCursorId !== null);
      } catch (error) {
      } finally {
        setLoading(false);
      }
    };
    fetchInitialJobs();
  }, [type]);

  const loadMoreJobs = () => {
    if (!cursorId || !hasMore || isLoadingMore) return;
    fetchJobs(true);
  };

  const onFilterPress = async () => {
    try{
      setLoadingFilters(true);
      const filters = await fetchFiltersForApplicantAPI();
      dispatch(
        setFilters({
          page: 'myJobs',
          filters: filters,
        }),
      );
    }catch(e){
      showToast({
        type:'error',
        message:'Failed to load Filters.Try Again Later'
      })
    }finally{
      setLoadingFilters(false);
    }
  };

  return {
    jobs,
    searchText,
    navigation,
    filterTabs,
    isLoadingMore,
    loading,
    loadingFilters,
    setSearchText,
    loadMoreJobs,
    fetchJobs,
    onFilterPress,
  };
};

const transformStatus = (type: string) => {
  const status = {
    applied: 'PENDING',
    shortlisted: 'SHORTLISTED',
    offered: 'OFFERED',
  };

  return status[type as keyof typeof status];
};
