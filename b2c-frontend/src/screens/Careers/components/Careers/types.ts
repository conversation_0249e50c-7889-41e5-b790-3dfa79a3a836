import { FC, JSX } from 'react';
import { IdNameTypeI, ImoNameDataTypeI } from '@/src/types/common/data';
import { FilledIconPropsI, OutlinedIconPropsI } from '@/src/assets/svgs/types';
import { ProfileExternalI } from '@/src/networks/connect/types';
import { EntityProfileBaseI } from '@/src/networks/entityProfile/types';

export type optionScreensI = {
  jobs: JSX.Element;
  courses: JSX.Element;
};

export type bottomSheetItemPropsI = {
  id: string;
  label: string;
  icon: FC<OutlinedIconPropsI> | FC<FilledIconPropsI>;
  onPress: () => void;
};

export type JobI = {
  applicationStatus?: string | null;
  createdAt?: string;
  creator?: ProfileExternalI;
  cursorId: string | null;
  department?: IdNameTypeI;
  designation: IdNameTypeI;
  entity: IdNameTypeI;
  expiryDate?: string;
  id: string;
  isOfficial?: boolean;
  isUrgent?: boolean;
  matching?: number | null;
  maxSalary?: number | null;
  maxYears?: number | null;
  minSalary?: number | null;
  minYears?: number;
  ship?: ImoNameDataTypeI;
  status?: string | null;
  entityProfile?:EntityProfileBaseI
};

export type CareersPropsI = {
  onBack: () => void;
};
