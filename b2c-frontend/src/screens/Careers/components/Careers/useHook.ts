import { useCallback, useEffect, useState } from 'react';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useDispatch, useSelector } from 'react-redux';
import { IdLabelAppliedCountI } from '@/src/components/CareerFilterBar/types';
import { selectActiveFilterNumbers, selectActiveFilters } from '@/src/redux/selectors/careers';
import { setFilters } from '@/src/redux/slices/career/careerSlice';
import { AppDispatch } from '@/src/redux/store';
import { IdLabelI } from '@/src/types/common/data';
import { CareerStackParamListI } from '@/src/navigation/types';
import Job from '@/src/assets/svgs/Job';
import JobPosts from '@/src/assets/svgs/JobPosts';
import Settings from '@/src/assets/svgs/Settings';
import { fetchFiltersForCandidatesAPI } from '@/src/networks/jobs/fetchFIltersForCandidates';
import { fetchJobsForCandidatesAPI } from '@/src/networks/jobs/fetchJobsForCandidates';
import { bottomSheetItemPropsI, JobI } from './types';
import { showToast } from '@/src/utilities/toast';

const PAGE_SIZE = 10;

export const useCareers = () => {
  const navigation = useNavigation<StackNavigationProp<CareerStackParamListI>>();
  const [loading, setLoading] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [searchText, setSearchText] = useState('');
  const [cursorId, setCursorId] = useState<string | null>(null);
  const activeFilterNumbers = useSelector(selectActiveFilterNumbers('jobs'));
  const filters = useSelector(selectActiveFilters('jobs'));
  const [loadingFilters,setLoadingFilters] = useState(false);
  const dispatch = useDispatch<AppDispatch>();

  const bottomSheetItems: bottomSheetItemPropsI[] = [
    {
      id: 'my-jobs',
      label: 'My Jobs',
      icon: Job,
      onPress: () => navigation.navigate('MyJobs'),
    },
    {
      id: 'job-posts',
      label: 'Job Posts',
      icon: JobPosts,
      onPress: () => navigation.navigate('JobPosts'),
    },
    {
      id: 'preferences',
      label: 'Preferences',
      icon: Settings,
      onPress: () => /*navigation.navigate('MyJobs')*/ {},
    },
  ];

  const filterTabs: IdLabelAppliedCountI[] = [
    {
      id: 'location',
      label: 'Location',
      appliedCount: activeFilterNumbers.locations || 0,
    },
    {
      id: 'designation',
      label: 'Designation',
      appliedCount: activeFilterNumbers.designations || 0,
    },
    {
      id: 'internet',
      label: 'Internet',
      appliedCount: activeFilterNumbers.internetLimits || 0,
    },
    {
      id: 'ship-type',
      label: 'Ship Type',
      appliedCount: activeFilterNumbers.shipTypes || 0,
    },
  ];

  const [jobs, setJobs] = useState<JobI[]>([]);

  const fetchJobs = async (loadMore = false, reset = false) => {
    try {
      if (reset) {
        setCursorId(null);
        setJobs([]);
        setLoading(true);
      } else if (loadMore) {
        if (!cursorId || !hasMore) return;
        setIsLoadingMore(true);
      } else {
        setLoading(true);
      }

      const query = {
        pageSize: PAGE_SIZE,
        cursorId: reset ? null : cursorId,
      };
      const body = {
        designations: filters.designations,
        countries: filters.locations,
        shipTypes: filters.shipTypes,
        internetLimits: filters.internetLimits
      };
      const result = await fetchJobsForCandidatesAPI(query, body);
      if (loadMore) {
        setJobs((prev) => [...prev, ...result.data]);
      } else {
        setJobs(result.data);
      }

      setCursorId(result.nextCursorId);
      setHasMore(result.nextCursorId !== null);
    } catch (error) {
      showToast({
        type:'error',
        message:'Failed to load Filters.Try Again Later'
      })
    } finally {
      if (loadMore) {
        setIsLoadingMore(false);
      } else {
        setLoading(false);
      }
    }
  };

  useFocusEffect(
    useCallback(() => {
      fetchJobs();
    }, []),
  );

  const loadMoreJobs = () => {
    if (!cursorId || !hasMore || isLoadingMore) return;
    fetchJobs(true);
  };

  const onFilterPress = async () => {
    try{
      setLoadingFilters(true);
      const filters = await fetchFiltersForCandidatesAPI();
      dispatch(
        setFilters({
          page: 'jobs',
          filters: filters,
        }),
      );
    }catch(e){
      showToast({
        type:'error',
        message:'Failed to load Filters.Try Again Later'
      })
    }finally{
      setLoadingFilters(false)
    }
    
  };

  return {
    navigation,
    bottomSheetItems,
    searchText,
    jobs,
    filterTabs,
    loading,
    isLoadingMore,
    loadingFilters,
    loadMoreJobs,
    setSearchText,
    fetchJobs,
    onFilterPress,
  };
};
