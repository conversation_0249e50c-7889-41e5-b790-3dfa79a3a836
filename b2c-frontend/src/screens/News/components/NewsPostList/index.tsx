/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import {
  View,
  Text,
  FlatList,
  Pressable,
  RefreshControl,
  ActivityIndicator,
  Linking,
  TouchableOpacity,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useDispatch } from 'react-redux';
import NotFound from '@/src/components/NotFound';
import { setNewsFilters, clearNews } from '@/src/redux/slices/news/newsSlice';
import type { AppDispatch } from '@/src/redux/store';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';
import NewsPost from '../NewsPost';
import NewsPostSkeleton from '../NewsPostSkeleton';
import useNewsPostList from './useHook';

const ListFooter = ({ isLoading }: { isLoading: boolean }) => {
  if (!isLoading) return null;
  return (
    <View className="py-4">
      <ActivityIndicator size="small" />
    </View>
  );
};

const NewsPostListSkeleton = () => (
  <View className="flex-1">
    {Array.from({ length: 5 }).map((_, index) => (
      <NewsPostSkeleton key={`news-skeleton-${index}`} />
    ))}
  </View>
);

const NewsPostList = () => {
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();
  const dispatch = useDispatch<AppDispatch>();
  const {
    news,
    loading,
    refreshing,
    hasMore,
    activeFilterCount,
    handleRefresh,
    handleLoadMore,
    selectedTopics,
    handleSelectNews,
  } = useNewsPostList();

  const handleClearFilters = async () => {
    dispatch(setNewsFilters({ topicId: null }));
    await handleRefresh();
  };

  const handleEndReached = () => {
    if (hasMore && !loading) {
      handleLoadMore();
    }
  };

  const handleNewsPress = async (url: string) => {
    try {
      const supported = await Linking.canOpenURL(url);
      if (supported) {
        await Linking.openURL(url);
      } else {
        console.log("Don't know how to open URI: " + url);
      }
    } catch (error) {
      console.error('Error opening URL:', error);
    }
  };

  return (
    <View className="flex-1 bg-white">
      {refreshing || (loading && news.length === 0) ? (
        <View className="flex-1">
          <NewsPostListSkeleton />
        </View>
      ) : (
        <>
          {/* Selected Topics Display */}
          {selectedTopics.length > 0 && (
            <View className="px-4 mb-4">
              <View className="flex-row justify-between items-center mb-2">
                <Text className="text-sm text-gray-600">Filtered by topics:</Text>
                <TouchableOpacity
                  onPress={handleClearFilters}
                  className="bg-red-100 rounded-full px-3 py-1"
                >
                  <Text className="text-red-600 text-sm font-medium">Clear Filters</Text>
                </TouchableOpacity>
              </View>
              <View className="flex-row flex-wrap">
                {selectedTopics.map((topic) => (
                  <View key={topic.id} className="bg-blue-100 rounded-full px-3 py-1 mr-2 mb-2">
                    <Text className="text-blue-800 text-sm">{topic.name}</Text>
                  </View>
                ))}
              </View>
            </View>
          )}

          {news.length === 0 && !loading ? (
            <NotFound title="No news articles found" />
          ) : (
            <FlatList
              data={news}
              keyExtractor={(item) => item.id}
              renderItem={({ item }) => (
                <NewsPost
                  news={{
                    id: item.id,
                    title: item.title,
                    link: item.link,
                    publishedDate: item.publishedDate,
                    source: item.provider.name,
                    scrapedAt: item.scrapedAt,
                    topics: item.topics,
                    onPress: () => handleSelectNews(item),
                  }}
                />
              )}
              onEndReached={handleEndReached}
              onEndReachedThreshold={0.1}
              refreshControl={<RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />}
              ListFooterComponent={<ListFooter isLoading={loading} />}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={{
                flexGrow: 1,
                backgroundColor: 'white',
                paddingBottom: 20,
              }}
            />
          )}
        </>
      )}
    </View>
  );
};

export default NewsPostList;
