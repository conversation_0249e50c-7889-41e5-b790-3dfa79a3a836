import { useCallback, useEffect, useRef, useState } from 'react';
import { Linking } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import {
  selectNews,
  selectNewsTopics,
  selectNewsFilters,
  selectNewsPagination,
  selectNewsLoading,
} from '@/src/redux/selectors/news';
import { fetchNews, fetchNewsTopics } from '@/src/redux/slices/news/newsSlice';
import type { AppDispatch } from '@/src/redux/store';
import { showToast } from '@/src/utilities/toast';
import APIResError from '@/src/errors/networks/APIResError';
import type { NewsItemI, NewsTopicI } from '@/src/networks/news/types';

const useNewsPostList = () => {
  const dispatch = useDispatch<AppDispatch>();
  const news = useSelector(selectNews);
  const topics = useSelector(selectNewsTopics);
  const filters = useSelector(selectNewsFilters);
  const pagination = useSelector(selectNewsPagination);
  const loading = useSelector(selectNewsLoading);

  const [refreshing, setRefreshing] = useState(false);
  const hasInitiallyFetched = useRef(false);

  useEffect(() => {
    if (!hasInitiallyFetched.current) {
      hasInitiallyFetched.current = true;

      const loadInitialData = async () => {
        try {
          // Load topics first
          await dispatch(fetchNewsTopics()).unwrap();

          // Then load news
          await dispatch(fetchNews({ refresh: true })).unwrap();
        } catch (error) {
          if (error instanceof APIResError) {
            showToast({ message: `Failed to load news: ${error.message}`, type: 'error' });
          } else {
            showToast({ message: 'Failed to load news', type: 'error' });
          }
        }
      };

      loadInitialData();
    }
  }, [dispatch]);

  const handleRefresh = useCallback(async () => {
    try {
      setRefreshing(true);
      await dispatch(fetchNews({ refresh: true })).unwrap();
    } catch (error) {
      showToast({ message: 'Failed to refresh news', type: 'error' });
    } finally {
      setRefreshing(false);
    }
  }, [dispatch]);

  const handleLoadMore = useCallback(async () => {
    if (loading.news || !pagination.hasMore) {
      return;
    }

    try {
      await dispatch(fetchNews({ refresh: false })).unwrap();
    } catch (error) {
      showToast({ message: 'Failed to load more news', type: 'error' });
    }
  }, [dispatch, loading.news, pagination.hasMore]);

  const handleSelectNews = useCallback((newsItem: NewsItemI) => {
    Linking.openURL(newsItem.link).catch(() => {
      showToast({ message: 'Failed to open news article', type: 'error' });
    });
  }, []);

  const getActiveFilterCount = useCallback(() => {
    return filters.topicId ? 1 : 0;
  }, [filters.topicId]);

  const activeFilterCount = getActiveFilterCount();

  return {
    news,
    topics,
    loading: loading.news,
    refreshing,
    hasMore: pagination.hasMore,
    activeFilterCount,
    selectedTopics: filters.topicId ? topics.filter((topic) => topic.id === filters.topicId) : [],
    handleRefresh,
    handleLoadMore,
    handleSelectNews,
  };
};

export default useNewsPostList;
