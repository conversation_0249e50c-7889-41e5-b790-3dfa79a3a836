/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { View, Text, Pressable } from 'react-native';
import { formatNewsDate } from '@/src/utilities/date';
import type { NewsItemI, NewsTopicI } from '@/src/networks/news/types';

interface NewsPostProps {
  news: {
    id: string;
    title: string;
    link: string;
    publishedDate: string;
    source: string;
    scrapedAt: string;
    topics: NewsTopicI[];
    onPress: () => void;
  };
}

const NewsPost = ({ news }: NewsPostProps) => {
  return (
    <Pressable
      onPress={news.onPress}
      className="bg-white overflow-hidden py-4 px-4 border-b border-gray-200 mb-3 active:bg-gray-50"
    >
      <Text className="text-lg font-semibold text-gray-900 mb-2 leading-6">{news.title}</Text>

      <View className="flex-row items-center justify-between mb-3">
        <Text className="text-sm text-gray-600">{news.source}</Text>
        {news.publishedDate && (
          <Text className="text-sm text-gray-500">{formatNewsDate(news.publishedDate)}</Text>
        )}
      </View>

      {news.topics.length > 0 && (
        <View className="flex-row flex-wrap">
          {news.topics.map((topic) => (
            <View key={topic.id} className="bg-blue-100 rounded-full px-2 py-1 mr-2 mb-1">
              <Text className="text-blue-800 text-xs font-medium">{topic.name}</Text>
            </View>
          ))}
        </View>
      )}
    </Pressable>
  );
};

export default NewsPost;
