/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { View, Text, Pressable } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';
import Explore from '@/src/assets/svgs/Explore';
import Search from '@/src/assets/svgs/Search';

const TopBar = () => {
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();

  const handleSearch = () => {
    // Navigate to news search screen (to be created later)
    console.log('News search pressed');
  };

  const handleExplore = () => {
    // Navigate to news explore screen (to be created later)
    console.log('News explore pressed');
  };

  return (
    <View className="flex-row items-center justify-between px-4 py-3 bg-white border-b border-gray-100">
      <Text className="text-xl font-bold text-gray-900">News & Updates</Text>

      <View className="flex-row items-center space-x-3">
        <Pressable onPress={handleSearch} className="p-2 rounded-full active:bg-gray-100">
          <Search width={20} height={20} color="#4B5563" />
        </Pressable>

        <Pressable onPress={handleExplore} className="p-2 rounded-full active:bg-gray-100">
          <Explore width={20} height={20} color="#4B5563" />
        </Pressable>
      </View>
    </View>
  );
};

export default TopBar;
