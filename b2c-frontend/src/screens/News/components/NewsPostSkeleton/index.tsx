/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { View } from 'react-native';

const NewsPostSkeleton = () => {
  return (
    <View className="bg-white overflow-hidden py-4 px-4 border-b border-gray-200 mb-3">
      {/* Title skeleton */}
      <View className="bg-gray-200 h-6 rounded mb-2" />
      <View className="bg-gray-200 h-6 rounded w-3/4 mb-3" />

      {/* Source and date skeleton */}
      <View className="flex-row items-center justify-between mb-3">
        <View className="bg-gray-200 h-4 rounded w-24" />
        <View className="bg-gray-200 h-4 rounded w-20" />
      </View>

      {/* Topics skeleton */}
      <View className="flex-row flex-wrap">
        <View className="bg-gray-200 h-6 rounded-full w-16 mr-2 mb-1" />
        <View className="bg-gray-200 h-6 rounded-full w-20 mr-2 mb-1" />
      </View>
    </View>
  );
};

export default NewsPostSkeleton;
