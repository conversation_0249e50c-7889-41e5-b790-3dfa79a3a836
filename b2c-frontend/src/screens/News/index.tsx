// /*
// Copyright (c) 2025-present Navicater Solutions

// This source code is licensed under the license found in the
// LICENSE file in the root directory of this source tree.
// */
// import React from 'react';
// import { View, Text, Pressable } from 'react-native';
// import { useNavigation } from '@react-navigation/native';
// import { StackNavigationProp } from '@react-navigation/stack';
// import { useSelector } from 'react-redux';
// import SafeArea from '@/src/components/SafeArea';
// import BackButton from '@/src/components/BackButton';
// import { LearnCollabStackParamsListI } from '@/src/navigation/types';
// import { selectNewsTopics } from '@/src/redux/selectors/news';
// import Filter from '@/src/assets/svgs/Filter';
// import ExploreContained from '@/src/assets/svgs/ExploreContained';
// import NewsPostList from './components/NewsPostList';

// const NewsScreen = () => {
//   const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();
//   const newsTopics = useSelector(selectNewsTopics);

//   const handleFilter = () => {
//     // TODO: Implement news filtering functionality similar to Forum
//     console.log('News filter pressed - to be implemented');
//   };

//   const handleExplore = () => {
//     if (newsTopics.length === 0) {
//       console.log('News topics not loaded yet');
//       return;
//     }

//     // Navigate to ExploreNews with all topics available for filtering
//     navigation.navigate('ExploreNews', {
//       topicIds: [], // Start with no filter, user can select topics in explore screen
//     });
//   };

//   return (
//     <SafeArea>
//       <View className="flex-row justify-between items-center px-4 py-2 bg-white">
//         <View className="flex-row items-center">
//           <BackButton onBack={() => navigation.goBack()} label="" />
//           <Text className="text-2xl font-semibold text-black">News & Updates</Text>
//         </View>
//         <View className="flex-row items-center gap-6 mr-6">
//           <Pressable onPress={handleExplore} className="items-center justify-center pr-2">
//             <ExploreContained color="#448600" width={3} height={3} />
//             <Text className="text-xs text-black">Explore</Text>
//           </Pressable>

//           <Pressable onPress={handleFilter} className="items-center justify-center pr-2">
//             <Filter color="#448600" width={3} height={3} />
//             <Text className="text-xs text-black">Filter</Text>
//           </Pressable>
//         </View>
//       </View>
//       <View className="flex-1 mt-4">
//         <NewsPostList />
//       </View>
//     </SafeArea>
//   );
// };

// export default NewsScreen;
