import { useCallback, useEffect, useMemo, useState } from 'react';
import { ActivityIndicator, View, Text, TouchableOpacity, Pressable } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { ScrollView } from 'react-native-gesture-handler';
import { useDispatch, useSelector } from 'react-redux';
import NotFound from '@/src/components/NotFound';
import { selectNewsTopics, selectNewsLoading } from '@/src/redux/selectors/news';
import { fetchNewsTopics } from '@/src/redux/slices/news/newsSlice';
import type { AppDispatch } from '@/src/redux/store';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';
import type { NewsTopicI } from '@/src/networks/news/types';
import ExploreHead from '../ExploreHead';
import ExploreQnA from '../ExploreQna';
import ExploreTroubleShoot from '../ExploreTroubleShoot';
import RecommendedCommunities from '../RecommendedCommunities';
import useExploreForum from './useHook';

const INITIAL_NEWS_TOPICS_COUNT = 6;

const ExploreForum = () => {
  const { loading, topics, recommendedCommunities } = useExploreForum();
  const dispatch = useDispatch<AppDispatch>();
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();

  // News topics state
  const newsTopics = useSelector(selectNewsTopics);
  const newsLoading = useSelector(selectNewsLoading);
  const [showAllNewsTopics, setShowAllNewsTopics] = useState(false);

  // Fetch news topics
  useEffect(() => {
    if (newsTopics.length === 0) {
      dispatch(fetchNewsTopics());
    }
  }, [dispatch, newsTopics.length]);

  // News topic handlers
  const handleNewsTopicPress = useCallback(
    (topic: NewsTopicI) => {
      navigation.navigate('ExploreNews', { topicId: topic.id });
    },
    [navigation],
  );

  const handleAllNewsPress = useCallback(() => {
    navigation.navigate('ExploreNews', { topicId: undefined });
  }, [navigation]);

  const handleSeeMoreNewsTopics = useCallback(() => {
    setShowAllNewsTopics(true);
  }, []);

  const displayedNewsTopics = useMemo(
    () => (showAllNewsTopics ? newsTopics : newsTopics.slice(0, INITIAL_NEWS_TOPICS_COUNT)),
    [showAllNewsTopics, newsTopics],
  );

  const renderNewsTopics = useCallback(() => {
    if (newsLoading.topics) {
      return (
        <View className="py-4 items-center">
          <ActivityIndicator size="small" color="#448600" />
          <Text className="mt-2 text-gray-600 text-sm">Loading news topics...</Text>
        </View>
      );
    }

    if (newsTopics.length === 0) {
      return (
        <View className="py-4 items-center">
          <Text className="text-gray-500 text-center text-sm">No news topics available</Text>
        </View>
      );
    }

    return (
      <View className="p-4 bg-gray-50">
        <Text className="text-labelBlack text-lg font-medium leading-5 mb-4">
          Explore News Topics
        </Text>

        <View className="flex-row flex-wrap gap-2 mb-4">
          <TouchableOpacity
            onPress={handleAllNewsPress}
            className="bg-white border border-chipGray rounded-full py-3 px-4"
            activeOpacity={0.7}
          >
            <Text className="text-center text-black text-sm">All News</Text>
          </TouchableOpacity>
          {displayedNewsTopics.map((topic) => (
            <TouchableOpacity
              key={topic.id}
              onPress={() => handleNewsTopicPress(topic)}
              className="bg-white border border-chipGray rounded-full py-3 px-4"
              activeOpacity={0.7}
            >
              <Text className="text-center text-black text-sm">{topic.name}</Text>
            </TouchableOpacity>
          ))}
        </View>

        {!showAllNewsTopics && newsTopics.length > INITIAL_NEWS_TOPICS_COUNT && (
          <Pressable onPress={handleSeeMoreNewsTopics}>
            <Text className="text-primaryGreen text-base font-medium">See More</Text>
          </Pressable>
        )}
      </View>
    );
  }, [
    newsLoading.topics,
    newsTopics.length,
    displayedNewsTopics,
    handleNewsTopicPress,
    showAllNewsTopics,
    handleSeeMoreNewsTopics,
  ]);

  return loading ? (
    <View className="py-6">
      <ActivityIndicator />
    </View>
  ) : (
    <View>
      <ExploreHead />
      <ScrollView>
        <View className="mb-40">
          <ExploreQnA title="Explore QnA" tags={topics} showAllText="See More" />
          {topics.length === 0 ? (
            <NotFound
              title="No topics found"
              subtitle="Try again later or explore troubleshooting posts"
              fullScreen={false}
              className="py-4"
              titleClassName="text-base font-medium"
            />
          ) : null}

          {/* News Topics Section */}
          {renderNewsTopics()}

          <ExploreTroubleShoot />
          <RecommendedCommunities communities={recommendedCommunities} />
        </View>
      </ScrollView>
    </View>
  );
};

export default ExploreForum;
