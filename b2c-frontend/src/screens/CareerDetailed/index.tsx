import React from 'react';
import { View } from 'react-native';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { ScrollView } from 'react-native-gesture-handler';
import BackButton from '@/src/components/BackButton';
import SafeArea from '@/src/components/SafeArea';
import { CareerStackParamListI, RootStackParamListI } from '@/src/navigation/types';
import CareerDetailed from './components/CareerDetailed';

const CareerDetailedScreen = () => {
  const navigation = useNavigation();
  const route = useRoute<RouteProp<CareerStackParamListI, 'CareerDetails'>>();
  const jobId = route.params.jobPostId;
  const handleBack = () => {
    navigation.goBack();
  };
  return (
    <SafeArea>
      <View className='pb-40'>
        <BackButton onBack={handleBack} label="Careers" labelClassname="text-xl font-bold" />
        <ScrollView 
          showsVerticalScrollIndicator={false}
        >
          <CareerDetailed jobId={jobId} />
        </ScrollView>
      </View>
    </SafeArea>
  );
};

export default CareerDetailedScreen;
