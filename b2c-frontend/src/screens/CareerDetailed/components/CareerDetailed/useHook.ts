import { useEffect, useMemo, useState } from 'react';
import { showToast } from '@/src/utilities/toast';
import { updateJobApplication } from '@/src/networks/company/job/application';
import { useSelector } from 'react-redux';
import { selectCurrentEntityProfile } from '@/src/redux/selectors/entityProfile';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { CareerStackParamListI } from '@/src/navigation/types';
import { formatElapsedTime } from '@/src/utilities/datetime';
import { fetchJobForCandidate } from '@/src/networks/company/job/job';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { CargosI, CertificationRequirementsI, DocumentRequirementsI, EquipmentRequirementsI, ExperienceRequirementsI, OtherRequirementsI, skillRequirementsI } from '@/src/networks/company/job/types';
import { BenefitsI } from './types';

const useJobActions = ({ jobId }: { jobId: string }) => {
  const [applicationId, setApplicationId] = useState<string | undefined>(undefined);
  const [isApplied, setIsApplied] = useState(false);
  const [withdrawing, setWithdrawing] = useState(false);
  const currentUser = useSelector(selectCurrentUser);
  const currentEntity = useSelector(selectCurrentEntityProfile);
  const [isApplicantsVisible,setIsApplicantsVisible] = useState(false);
  const navigation = useNavigation<StackNavigationProp<CareerStackParamListI>>();
  const [optionsVisible, setOptionsVisible] = useState(false);
  const [closeModalVisible, setCloseModalVisible] = useState(false); // shared confirm modal visibility
  const [closing, setClosing] = useState(false);
  const [confirmAction, setConfirmAction] = useState<'withdraw' | 'close' | null>(null);
  const [openWithdrawAfterSheet, setOpenWithdrawAfterSheet] = useState(false);
  const [isClosed, setIsClosed] = useState(false);
  const [loading,setLoading] = useState(false);

  const canActOnJob = !!jobId;

  const [displayTitle, setDisplayTitle] = useState<string|null>(null);
  const [displayCompanyName, setDisplayCompanyName] = useState<string|null>(null);
  const [displayMatchPercent, setDisplayMatchPercent] = useState(0);
  const [displayPostedAgoText, setDisplayPostedAgoText] = useState<string|null>(null);
  const [displayLocationText,setDisplayLocationText] = useState('Not specified');
  const [displayAboutText,setDisplayAboutText] = useState('Not specified');
  const [displayRolesResponsibilitiesText,setDisplayRolesResponsibilitiesText] = useState('Not specified');
  const [displayRequirementsText,setDisplayRequirementsText] = useState<string|null>(null);
  const [displayBenefitsText,setDisplayBenefitsText] = useState<string|null>(null);

  const [certificates,setCertificates] = useState<CertificationRequirementsI[]>([])
  const [documents,setDocuments] = useState<DocumentRequirementsI[]>([])
  const [experiences,setExperiences] = useState<ExperienceRequirementsI[]>([]);
  const [equipments,setEquipments] = useState<EquipmentRequirementsI[]>([])
  const [cargos,setCargos] = useState<CargosI[]>([])
  const [skills,setSkills] = useState<skillRequirementsI[]>([]);
  const [otherRequirements,setOtherRequirements] = useState<OtherRequirementsI[]>([]);
  const [benefits,setBenefits] = useState<BenefitsI>()

  const [showShipDetails,setShowShipDetails] = useState(false);
  const [shipType,setShipType] = useState('');
  const [shipImo,setShipImo] = useState('');
  const [jobType,setJobType] = useState('');
  const [genderEquityIndex,setGenderEquityIndex] = useState('');


  useEffect(() => {
    let isMounted = true;
    const load = async () => {
      if (!jobId) return;
      try {
        setLoading(true)
        const data = await fetchJobForCandidate(jobId);
        if (!isMounted || !data) return;
        if (data?.designation?.name) setDisplayTitle(data.designation.name);
        if (data?.entity?.name) setDisplayCompanyName(data.entity.name);
        if (typeof data?.matching === 'number') setDisplayMatchPercent(data.matching);
        if (data?.createdAt) setDisplayPostedAgoText(formatElapsedTime(data.createdAt));
        if (data?.applicationStatus) setIsApplied(data.applicationStatus === 'PENDING');
        if(data?.countryName)  setDisplayLocationText(data.countryName)
        if(data?.about) setDisplayAboutText(data.about)
        if(data?.rolesResponsibilities) setDisplayRolesResponsibilitiesText(data.rolesResponsibilities)
        if(data?.jobType) setJobType(data?.jobType)
        if(data?.ship) setShipImo(data?.ship.imo)
        if(data?.showShipDetails) setShowShipDetails(data?.showShipDetails)
        if(data?.genderDiversityIndex) setGenderEquityIndex(data.genderDiversityIndex)
        if(data?.shipType) setShipType(data.shipType.name)
        if(data?.requirementType === 'BASIC'){
          setDisplayRequirementsText(data?.requirements!)
        }else{
          if(data?.certificationRequirements){
            setCertificates(data.certificationRequirements)
          }
          if(data?.documentRequirements){
            setDocuments(data.documentRequirements)
          }
          if(data?.cargoRequirements){
            setCargos(data.cargoRequirements)
          }
          if(data?.skillRequirements){
            setSkills(data.skillRequirements)
          }
          if(data?.otherRequirements){
            setOtherRequirements(data.otherRequirements)
          }
          if(data?.equipmentRequirements){
            setEquipments(data.equipmentRequirements)
          }
          if(data?.experienceRequirements){
            setExperiences(data.experienceRequirements)
          }
        }
        if(data?.benefitType === 'BASIC'){
          setDisplayBenefitsText(data?.benefits!)
        } else {
          let benefits: BenefitsI = {
            internetDetails: {},
            insuranceDetails: {},
            salaryDetails: {},
            contractDetails: {}
          };

          if (data) {
            benefits.internetDetails = {
              internetAvailable: data.internetAvailable ?? true,
              internetSpeed: data.internetSpeed ?? 0,
              internetLimit: data.internetLimitPerDay ?? 0,
              details: data.internetDetails ?? 'nil'
            };

            benefits.insuranceDetails = {
              insuranceType: data.insuranceType ?? 'nil',
              itfType: data.itfType ?? 'nil',
              familyOnboard: data.familyOnboard ?? null
            };

            benefits.salaryDetails = {
              showSalary: data.showSalary ?? false,
              maxSalary: data.maxSalary ?? 0,
              minSalary: data.minSalary ?? 0,
              salaryType: data.salaryType ?? 'nil',
              currencyCode: data.currencyCode ?? 'nil'
            };

            benefits.contractDetails = {
              contractDays: data.contractDays,
              contractMonths: data.contractMonths
            };
          }

          setBenefits(benefits);
        }
        if(currentUser.isActive){
          if(data.creator.id === currentUser.profileId){
            setIsApplicantsVisible(true)
          }
        }else{
          if(data.entity?.id === currentEntity.entity.id){
            setIsApplicantsVisible(true)
          }
        }
      } catch (_e) {
        showToast({ message: 'Error fetching job details', type: 'error' });
      }finally{
        setLoading(false)
      }
    };
    load();
    return () => {
      isMounted = false;
    };
  }, [jobId]);

  const handleApply = async () => {
    if (!canActOnJob) {
      showToast({ message: 'Missing job information', type: 'error' });
      return;
    }
    try {
      const result = await updateJobApplication({ jobId: jobId!, status: 'PENDING' });
      setApplicationId(result.id);
      setIsApplied(true);
      showToast({ message: 'Applied successfully', type: 'success' });
    } catch (error) {
      showToast({ message: (error as {message:string}).message, type: 'error' });
    }
  };

  const handleWithdraw = async () => {
    if (!canActOnJob || !applicationId) {
      showToast({ message: 'No application to withdraw', type: 'error' });
      return;
    }
    try {
      setWithdrawing(true);
      await updateJobApplication({ jobId: jobId!, status: 'WITHDREW', applicationId });
      setIsApplied(false);
      showToast({ message: 'Application withdrawn', type: 'success' });
    } catch (_e) {
      showToast({ message: 'Failed to withdraw', type: 'error' });
    } finally {
      setWithdrawing(false);
    }
  };

  const handleApplications = () => {
    navigation.navigate('Applicants', { jobId });
  };

  const handleOptions = () => setOptionsVisible(true);
  const onWithdrawPress = () => {
    setConfirmAction('withdraw');
    setOpenWithdrawAfterSheet(true);
    setOptionsVisible(false);
  };

  const showFields = 
    certificates.length > 0 ||
    documents.length > 0 ||
    experiences.length > 0 ||
    equipments.length > 0 ||
    cargos.length > 0 ||
    skills.length > 0 ||
    otherRequirements.length > 0 ||
    (benefits && (
      benefits.internetDetails || 
      benefits.insuranceDetails || 
      benefits.salaryDetails || 
      benefits.contractDetails
    ));

  return {
    handleApply,
    handleOptions,
    handleWithdraw,
    onWithdrawPress,
    handleApplications,
    setClosing,
    setIsClosed,
    setCloseModalVisible,
    isApplied,
    setIsApplied,
    setConfirmAction,
    setOptionsVisible,
    setOpenWithdrawAfterSheet,
    withdrawing,
    applicationId,
    isApplicantsVisible,
    optionsVisible,
    closeModalVisible,
    closing,
    confirmAction,
    openWithdrawAfterSheet,
    isClosed,
    displayTitle,
    displayCompanyName,
    displayMatchPercent,
    displayPostedAgoText,
    displayLocationText,
    displayAboutText,
    displayRolesResponsibilitiesText,
    displayRequirementsText,
    displayBenefitsText,
    loading,
    certificates,
    documents,
    cargos,
    skills,
    otherRequirements,
    benefits,
    equipments,
    experiences,
    showShipDetails,
    shipType,
    shipImo,
    jobType,
    genderEquityIndex,
    showFields,
    navigation
  };
};

export default useJobActions;
