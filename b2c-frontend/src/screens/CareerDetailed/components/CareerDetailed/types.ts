export type JobHeadPropsI = {
  jobId: string;
};

export type BenefitsI = {
  internetDetails:{
    internetAvailable?: boolean;
    internetSpeed?: number;
    internetLimit?: number;
    details?: string;
  }
  insuranceDetails:{
    insuranceType?:string;
    itfType?:string;
    familyOnboard?:boolean | null
  }
  salaryDetails:{
    showSalary?:boolean;
    maxSalary?:number | null;
    minSalary?:number,
    salaryType?:string | null;
    currencyCode?:string | null
  }
  contractDetails:{
    contractDays?:number | null;
    contractMonths?:number | null
  }
}