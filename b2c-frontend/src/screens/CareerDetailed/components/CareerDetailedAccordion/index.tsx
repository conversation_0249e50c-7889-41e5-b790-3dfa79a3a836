import Accordion from "@/src/components/Accordion"
import BottomSheet from "@/src/components/Bottomsheet"
import { Pressable, Text, View } from "react-native"
import { CareerDetailedAccordionPropsI } from "./types"
import ChevronDown from "@/src/assets/svgs/ChevronDown"
import { useState } from "react"
import ScrollableBottomSheet from "@/src/components/ScrollableBottomSheet"

const CareerDetailedAccordion = ({
    label,
    children
}:CareerDetailedAccordionPropsI) => {
    const [visible,setVisible] = useState(false);
    return(
        <Pressable className="py-3 border border-[#D4D4D4] rounded-xl p-5" onPress={() => setVisible(true)}>
            <View className="flex-row items-center justify-between" >
                <Text>{label}</Text>
                <View>
                    <ChevronDown />
                </View>
            </View>
            <ScrollableBottomSheet
                visible={visible}
                onClose={() => setVisible(false)}
                onModalHide={()=>{}}
                height={100}
            >
                {children}
            </ScrollableBottomSheet>
        </Pressable>  
    )
}

export default CareerDetailedAccordion