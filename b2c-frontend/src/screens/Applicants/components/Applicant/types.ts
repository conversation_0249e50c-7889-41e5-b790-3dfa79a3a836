import { IdNameI } from '@/src/types/common/data';
import { ProfileExternalI } from '@/src/networks/answerVote/types';

export type ApplicantI = {
  ApplicantProfile: ProfileExternalI;
  DecisionMakerProfile: ProfileExternalI;
  cursorId: string | null;
  id: string;
  matching: number;
  status: string;
  createdAt: string;
};

export type ApplicantPropsI = {
  applicant: ApplicantI;
  onStatusChanged?: () => void;
};
