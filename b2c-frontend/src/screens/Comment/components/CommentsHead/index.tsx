import { ActivityIndicator, Text, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import UserPost from '@/src/components/UserPost';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { savePostOptimistic, unsavePostOptimistic } from '@/src/redux/slices/content/contentSlice';
import { AppDispatch, RootState } from '@/src/redux/store';
import { isEmpty } from '@/src/utilities/data/object';
import { showToast } from '@/src/utilities/toast';
import { BottomTabNavigationI } from '@/src/navigation/types';
import { savePostAPI, unsavePostAPI } from '@/src/networks/content/post';
import { CommentsHeadProps } from './types';
import useCommentsHead from './useCommentsHead';

const CommentsHead = ({
  postId,
  type,
  onPostLoaded,
  portUnLocode,
  parentScrollRef,
  postIndex,
}: CommentsHeadProps) => {
  const currentUser = useSelector(selectCurrentUser);
  const navigation = useNavigation<BottomTabNavigationI>();
  const dispatch = useDispatch<AppDispatch>();

  const { loading, handleLikePost, handleDeletePost } = useCommentsHead(postId, type, onPostLoaded);

  const reduxPost = useSelector((state: RootState) => {
    if (state.content.post?.id === postId) {
      return state.content.post;
    }
    const homePost = state.content.posts.find((p) => p.id === postId);
    if (homePost) return homePost;
    const ownPost = state.content.ownPosts.find((p) => p.id === postId);
    if (ownPost) return ownPost;
    const savedPost = state.content.savedPosts.find((p) => p.id === postId);
    if (savedPost) return savedPost;
    for (const profileId in state.content.profilePosts) {
      const profilePost = state.content.profilePosts[profileId]?.posts.find((p) => p.id === postId);
      if (profilePost) return profilePost;
    }
    for (const searchTerm in state.content.searchPosts) {
      const searchPost = state.content.searchPosts[searchTerm].find((p) => p.id === postId);
      if (searchPost) return searchPost;
    }
    return null;
  });

  const comments = useSelector((state: RootState) => {
    if (type === 'SCRAPBOOK_POST') {
      return state.content.scrapbookComments[postId];
    }
    return state.content.comments[postId];
  });

  const post = reduxPost;

  const calculateCommentsCount = () => {
    if (!comments?.comments) return 0;

    let totalCount = comments.comments.length;

    comments.comments.forEach((comment) => {
      if (comment.repliesCount) {
        totalCount += comment.repliesCount;
      }
    });

    return totalCount;
  };

  const getCommentCount = () => {
    if (comments?.comments && comments.comments.length > 0) {
      return calculateCommentsCount();
    }
    return post?.totalCommentsCount || 0;
  };

  const handleSavePost = async (postId: string) => {
    dispatch(savePostOptimistic({ postId }));

    try {
      await savePostAPI(postId);
      showToast({
        type: 'success',
        message: 'Post saved successfully',
      });
    } catch (error) {
      dispatch(unsavePostOptimistic({ postId }));
      showToast({
        type: 'error',
        message: 'Failed to save post',
      });
    }
  };

  const handleUnsavePost = async (postId: string) => {
    dispatch(unsavePostOptimistic({ postId }));

    try {
      await unsavePostAPI(postId);
      showToast({
        type: 'success',
        message: 'Post removed from saved posts',
      });
    } catch (error) {
      dispatch(savePostOptimistic({ postId }));
      showToast({
        type: 'error',
        message: 'Failed to unsave post',
      });
    }
  };
  const finalCommentsCount = getCommentCount();

  if (loading && !post) {
    return (
      <View className="flex-1 justify-center items-center py-8">
        <ActivityIndicator size="large" color="#448600" />
        <Text className="mt-2 text-gray-600">Loading post...</Text>
      </View>
    );
  }

  if (!post || isEmpty(post)) {
    return (
      <View className="flex-1 justify-center items-center py-8">
        <Text className="text-gray-500">Post not found</Text>
      </View>
    );
  }

  const isOwnPost = currentUser?.profileId === post?.Profile?.id;

  return (
    <View>
      <UserPost
        post={{ ...post, totalCommentsCount: finalCommentsCount }}
        isOwnPost={isOwnPost}
        onLikePress={() => handleLikePost(post)}
        onDeletePress={() => handleDeletePost(post)}
        onSavePress={() => handleSavePost(post.id)}
        onUnsavePress={() => handleUnsavePost(post.id)}
        onEditPress={() =>
          navigation.navigate('CreateStack', {
            screen: 'CreateContent',
            params: {
              editing: true,
              postId: post.id,
              type,
              ...(type === 'SCRAPBOOK_POST' ? { portUnLocode } : {}),
            },
          })
        }
        onLikeCountPress={() =>
          navigation.navigate('HomeStack', {
            screen: 'Likes',
            params: { postId: post.id, type: type },
          })
        }
        onCommentPress={() =>
          navigation.navigate('HomeStack', {
            screen: 'Comment',
            params: {
              postId: post.id,
              type: type,
            },
          })
        }
        parentScrollRef={parentScrollRef}
        postIndex={postIndex}
      />
      <Text className="text-labelBlack font-medium text-sm leading-4 p-3">
        Comments {finalCommentsCount > 0 ? `(${finalCommentsCount})` : ''}
      </Text>
    </View>
  );
};

export default CommentsHead;
