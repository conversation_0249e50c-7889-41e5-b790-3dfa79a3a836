import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import FloatingActionButton from '@/src/components/FloatingActionButton';
import SafeArea from '@/src/components/SafeArea';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';
import CommunityList from './components/CommunityList';

const MyCommunitiesScreen = () => {
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();

  const handleCreateCommunity = () => {
    navigation.navigate('CreateCommunity');
  };

  return (
    <SafeArea>
      <CommunityList />
      <FloatingActionButton
        context="CREATE_COMMUNITY"
        onPress={handleCreateCommunity}
        visible={true}
      />
    </SafeArea>
  );
};

export default MyCommunitiesScreen;
