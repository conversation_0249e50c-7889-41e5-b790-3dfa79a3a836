import { GlobalSearchParams, GlobalSearchResponse } from '@/src/utilities/search/types';
import { apiCall } from '@/src/services/api';
import {
  OptionSearchBodyI,
  OptionSearchResultI,
  EntitySearchResponse,
  CountryOptionSearchResultI,
} from './types';

const ROUTE_MAP: Record<string, string> = {
  entity: '/backend/api/v1/company/entity/organization/options',
  country: '/backend/api/v1/master/country/options',
};

export const optionFetchSearchAPI = async (
  collection: string,
  text: string | null,
  page: string,
  type?: string,
): Promise<EntitySearchResponse> => {
  const query = text ? { search: text, page } : { page };
  const get = async <Req, Res>(url: string) =>
    apiCall<Req, Res>(url, 'GET', { isAuth: true, query: type ? { ...query, type } : query });

  if (collection.startsWith('designation')) {
    return get<OptionSearchBodyI, EntitySearchResponse>(
      '/backend/api/v1/company/designation/options',
    );
  }

  if (collection === 'country') {
    const result = await get<unknown, EntitySearchResponse>(ROUTE_MAP[collection]);
    const data: OptionSearchResultI[] = (result.data as CountryOptionSearchResultI[])
      .filter((i): i is CountryOptionSearchResultI => 'iso2' in i && !!i.iso2)
      .map((i) => ({ id: i.iso2, name: i.name, dataType: 'master' }));
    return { data, total: data.length };
  }

  if (collection === 'entity') {
    const base = '/backend/api/v1/company/entity';
    if (type === 'INSTITUTION')
      return get<OptionSearchBodyI, EntitySearchResponse>(`${base}/institute/options`);
    if (type === 'ORGANIZATION')
      return get<OptionSearchBodyI, EntitySearchResponse>(`${base}/organization/options`);
    return get<OptionSearchBodyI, EntitySearchResponse>(`${base}/all/options`);
  }

  if(collection === 'document') {
    const base = '/backend/api/v1/document/document-type'
    if(type === 'IDENTITY'){
      return get<OptionSearchBodyI, EntitySearchResponse>(`${base}/identity/options`);
    }
    if(type === 'VISA'){
      return get<OptionSearchBodyI, EntitySearchResponse>(`${base}/visa/options`);
    }
  }

  const endpointMap: Record<string, string> = {
    'certificate-course': '/backend/api/v1/company/certificate-course/options',
    ship: '/backend/api/v1/ship/search',
    subVesselType: '/backend/api/v1/ship/sub-vessel-type/options',
    category: '/backend/api/v1/ship/equipment-category/options',
    fuelType: '/backend/api/v1/ship/fuel-type/options',
    topic: '/backend/api/v1/forum/topic/options',
    filterTopic: '/backend/api/v1/forum/topic/options',
    equipmentCategory: '/backend/api/v1/ship/equipment-category/options',
    filterEquipmentCategory: '/backend/api/v1/ship/equipment-category/options',
    exploreEquipmentCategory: '/backend/api/v1/ship/equipment-category/options',
    equipmentManufacturer: '/backend/api/v1/ship/equipment-manufacturer/options',
    filterEquipmentManufacturer: '/backend/api/v1/ship/equipment-manufacturer/options',
    exploreEquipmentManufacturer: '/backend/api/v1/ship/equipment-manufacturer/options',
    equipmentModel: '/backend/api/v1/ship/equipment-model/options',
    filterEquipmentModel: '/backend/api/v1/ship/equipment-model/options',
    exploreEquipmentModel: '/backend/api/v1/ship/equipment-model/options',
    filterDepartment: '/backend/api/v1/company/department/options',
    city: '/backend/api/v1/master/city/options',
    'port-coordinates': '/backend/api/v1/port/port-coordinates/search',
    username: '/backend/api/v1/user/profile/usernames/search',
    mainVesselType:'/backend/api/v1/ship/main-vessel-type/options'
  };

  const endpoint = endpointMap[collection] || `/backend/api/v1/company/${collection}/options`;
  return get<unknown, EntitySearchResponse>(endpoint);
};

export const searchOrganizationGlobalAPI = async (query: GlobalSearchParams) =>
  apiCall<unknown, GlobalSearchResponse>(
    '/backend/api/v1/company/entity/organization/options',
    'GET',
    {
      isAuth: true,
      query,
    },
  );

export const searchInstituteGlobalAPI = async (query: GlobalSearchParams) =>
  apiCall<unknown, GlobalSearchResponse>(
    '/backend/api/v1/company/entity/institute/options',
    'GET',
    {
      isAuth: true,
      query,
    },
  );
