import { IdTypeI } from '@/src/types/common/data';
import { PostExternalClientI } from '../content/types';

export type CreateEntityProfileBodyI = {
  profileId: string;
  name: string;
  type: string;
  admins: string[];
  email: string;
  emailType?: string;
  entityId?: string;
  entityRawDataId?: string;
  website?: string;
  purpose?: string;
};

export type CreateEntityProfileResultI = {
  id: string;
};

export type sendOTPForEntityProfileEmailVerificationBodyI = {
  entityProfileId: string;
};

export type verifyOTPForEntityProfileEmailVerificationBodyI = {
  entityProfileId: string;
  otp: string;
};

export type entityProfileFetchAboutQueryI = {
  entityProfileId: string;
};

export type entityProfileFetchAboutResultI = {
  entityProfileId: string;
  name: string;
  avatar: string | null;
  description: string | null;
  overview: string | null;
  foundedAt: string | null;
  website: string | null;
  isFollowing: boolean;
  followersCount: number;
  followingsCount: number;
  entityType: string;
};

export type fetchEntityProfilesForUsersResultI = {
  avatar: string | null;
  id: string;
  name: string;
  isVerified: boolean;
  role: string;
  entity: IdTypeI;
};

export type fetchEntityProfilePostsQueryI = {
  cursorId: string | number | null;
  pageSize: number;
  entityProfileId: string;
};

export type fetchEntityProfilePostsResultI = {
  posts: PostExternalClientI[];
  cursorId: number;
};

export type entityProfileFetchBasicDetailsI = {
  entityProfileId: string;
  description: string;
  overview: string;
  foundedAt: string;
  website: string;
};

export type EntityProfileDetailsUpdateBodyI = {
  overview: string;
  description: string;
  foundedAt: string;
  website: string;
};

export type EntityProfileFollowQueryI = {
  followeeEntityProfileId: string;
};

export type EntityProfileUnfollowQueryI = {
  unfolloweeEntityProfileId: string;
};

export type AuthorEntityProfileI = {
  id: string;
  name: string;
  avatar: string | null;
  isFollowing: boolean;
};

export type fetchEntityProfileFollowersQueryI = {
  entityProfileId: string;
  pageSize: number;
  cursorId: null | string | number;
};

export type EntityProfileBaseI = {
  id: string;
  name: string;
  avatar: string | null;
};
