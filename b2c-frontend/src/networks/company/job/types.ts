import { IdNameCategoryI, IdNameI, IdNameTypeI, ImoNameDataTypeI, ProfileBaseI } from "@/src/types/common/data";

export type ApplicationStatusI = 'PENDING' | 'WITHDREW' | 'ACCEPTED_BY_APPLICANT';

export type UpsertApplicantApplicationPayloadI = {
  jobId: string;
  status: ApplicationStatusI;
  applicationId?: string;
};

export type UpsertApplicantApplicationResultI = { id: string };

export type JobEntityI = { id?: string; name?: string };
export type JobDesignationI = { id?: string; name?: string };

export type JobCandidateFetchOneResultI = {
  id: string;
  entity?: IdNameTypeI;
  designation?: IdNameTypeI
  matching?: number;
  createdAt?: string;
  about?: string | null;
  applicationEmail?:string | null
  applicationMethod?:string | null
  applicationStatus?:string | null
  applicationUrl?:string| null
  benefitType?:string | null
  benefits?:string | null
  countryIso2:string | null
  countryName?:string | null
  creator:ProfileBaseI
  currencyCode?:string | null;
  cursorId:string | null
  genderDiversityIndex?:string | null;
  isOfficial:boolean;
  isUrgent:boolean
  jobType?:string | null;
  joiningDate?:string | null;
  maxSalary?:number | null;
  maxYears?:string | null;
  minSalary?:number | null;
  minYears?:string | null;
  requirementType?:string | null;
  requirements?:string | null;
  rolesResponsibilities?:string | null;
  salaryType?:string | null;
  ship?:ImoNameDataTypeI | null;
  showSalary:boolean
  showShipDetails:boolean
  status?:string | null;
  certificationRequirements?:CertificationRequirementsI[]
  documentRequirements?:DocumentRequirementsI[]
  cargoRequirements?:CargosI[]
  skillRequirements?:skillRequirementsI[]
  otherRequirements?:OtherRequirementsI[]
  internetAvailable:boolean;
  internetDetails:string | null
  internetLimitPerDay:number | null
  internetSpeed:number| null;
  insuranceType:string | null;
  itfType:string | null
  familyOnboard:boolean | null;
  contractDays: number | null;
  contractMonths: number | null
  equipmentRequirements?:EquipmentRequirementsI[];
  experienceRequirements?:ExperienceRequirementsI[];
  shipType?:IdNameTypeI
};

export type EntityMemberApplicationStatusI = 'SHORTLISTED' | 'REJECTED_BY_ENTITY' | 'OFFERED';
export type UpdateEntityMemberApplicationStatusPayloadI = {
  applicationId: string;
  status: EntityMemberApplicationStatusI;
};

export type CertificationRequirementsI = {
  CertificateCourse:IdNameTypeI | null
  CertificateCourseRawData:IdNameTypeI | null
  certificateCourseId:string | null
  certificateCourseRawDataId:string | null
  isMandatory:boolean
}

export type DocumentRequirementsI = {
  DocumentType: IdNameCategoryI| null
  DocumentTypeRawData: IdNameCategoryI| null
  countries:string[]
  description:string
  isMandatory:boolean
  documentTypeId:string | null
  documentTypeRawDataId:string | null
}

export type CargosI = {
  name: string
  monthsOfExperience: number
  isMandatory:boolean
}

export type skillRequirementsI = {
  Skill:IdNameCategoryI | null
  SkillRawData:IdNameCategoryI | null
  isMandatory: boolean;
  skillId:string | null
  skillRawDataId:string | null
}

export type OtherRequirementsI = {
  details:string;
  isMandatory:boolean
}

export type EquipmentRequirementsI = {
  EquipmentCategory:IdNameI | null;
  EquipmentCategoryRawData:IdNameI | null
  EquipmentManufacturer : IdNameI | null
  EquipmentManufacturerRawData : IdNameI | null
  EquipmentModel : IdNameI | null
  EquipmentModelRawData : IdNameI | null
  FuelType: IdNameI | null
  FuelTypeRawData: IdNameI | null
  equipmentCategoryId:string | null
  equipmentCategoryRawDataId:string | null
  equipmentManufacturerId:string | null
  equipmentManufacturerRawDataId:string | null
  equipmentModelId:string | null
  equipmentModelRawDataId:string | null
  fuelTypeId:string | null
  fuelTypeRawDataId:string | null
  isMandatory:boolean
  monthsOfExperience:number
}

export type ExperienceRequirementsI = {
  DesignationAlternative:IdNameI | null;
  DesignationRawData:IdNameI | null
  SubVesselType:IdNameI | null
  SubVesselTypeRawData:IdNameI| null
  designationAlternativeId:string | null
  designationRawDataId:string | null
  isMandatory:boolean
  isTotal:boolean
  monthsOfExperience:number
  subVesselTypeId:string | null
  subVesselTypeRawDataId:string | null
}