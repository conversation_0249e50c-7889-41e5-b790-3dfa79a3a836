import { apiCall } from '@/src/services/api';
import { JobCandidateFetchOneResultI } from './types';

export const fetchJobForCandidate = async (jobId: string): Promise<JobCandidateFetchOneResultI> => {
  const result = await apiCall<string, JobCandidateFetchOneResultI>(
    `/backend/api/v1/company/job/candidate/${jobId}`,
    'GET',
    { isAuth: true },
  );
  return result;
};

export const closeJob = async (jobId: string): Promise<void> => {
  await apiCall<unknown, unknown>(`/backend/api/v1/company/job/core/${jobId}`, 'PATCH', {
    isAuth: true,
    payload: { expiryDate: new Date().toISOString() },
  });
};
