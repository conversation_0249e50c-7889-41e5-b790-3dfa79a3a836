import { apiCall } from '@/src/services/api';
import {
  fetchApplicantsForJobPostsBodyI,
  fetchApplicantsForJobPostsQueryI,
  fetchApplicantsForJobPostsResultI,
} from './types';

export const fetchApplicantsForJobPostsAPI = async (
  query: fetchApplicantsForJobPostsQueryI,
  payload: fetchApplicantsForJobPostsBodyI,
): Promise<fetchApplicantsForJobPostsResultI> => {
  const result = await apiCall<fetchApplicantsForJobPostsBodyI, fetchApplicantsForJobPostsResultI>(
    '/backend/api/v1/company/job/applications/entity-member',
    'POST',
    {
      isAuth: true,
      query,
      payload,
    },
  );

  return result;
};
