import { apiCall } from "@/src/services/api";
import { CreateJobStageOneBodyI, CreateJobStageOneResultI, CreateJobStageTwoBodyI } from "./types";

export const createJobStageOneAPI = async (
  payload: CreateJobStageOneBodyI,
): Promise<CreateJobStageOneResultI> => {
  const result = await apiCall<CreateJobStageOneBodyI, CreateJobStageOneResultI>(
    '/backend/api/v1/company/job/details',
    'POST',
    {
      isAuth: true,
      payload,
    },
  );

  return result;
};

export const createJobStageTwoAPI = async (
    id:string,
  payload: CreateJobStageTwoBodyI,
): Promise<CreateJobStageOneResultI> => {
  const result = await apiCall<CreateJobStageTwoBodyI, CreateJobStageOneResultI>(
    `/backend/api/v1/company/job/${id}/requirements-mobile`,
    'PATCH',
    {
      isAuth: true,
      payload,
    },
  );

  return result;
};

export const fetchDraftJobAPI = async (
  id: string,
): Promise<any> => {
  const result = await apiCall<any, any>(
    `/backend/api/v1/company/job/draft/${id}`,
    'GET',
    {
      isAuth: true,
    },
  );

  return result;
};

export const updateJobDetailsAPI = async (
  id: string,
  payload: CreateJobStageOneBodyI,
  isEdit: boolean = false,
): Promise<CreateJobStageOneResultI> => {
  const endpoint = isEdit
    ? `/backend/api/v1/company/job/${id}/edit/details`
    : `/backend/api/v1/company/job/${id}/details`;

  const result = await apiCall<CreateJobStageOneBodyI, CreateJobStageOneResultI>(
    endpoint,
    'PATCH',
    {
      isAuth: true,
      payload,
    },
  );

  return result;
};

export const updateJobRequirementsAPI = async (
  id: string,
  payload: CreateJobStageTwoBodyI,
  isEdit: boolean = false,
): Promise<CreateJobStageOneResultI> => {
  const endpoint = isEdit
    ? `/backend/api/v1/company/job/${id}/edit/requirements-mobile`
    : `/backend/api/v1/company/job/${id}/requirements-mobile`;

  const result = await apiCall<CreateJobStageTwoBodyI, CreateJobStageOneResultI>(
    endpoint,
    'PATCH',
    {
      isAuth: true,
      payload,
    },
  );

  return result;
};

export const fetchJobForEditingAPI = async (
  id: string,
): Promise<any> => {
  const result = await apiCall<any, any>(
    `/backend/api/v1/company/job/edit/${id}`,
    'GET',
    {
      isAuth: true,
    },
  );

  return result;
};

export const fetchJobDetailsForEditingAPI = async (
  id: string,
): Promise<any> => {
  const result = await apiCall<any, any>(
    `/backend/api/v1/company/job/edit/${id}/details`,
    'GET',
    {
      isAuth: true,
    },
  );

  return result;
};

export const fetchJobRequirementsForEditingAPI = async (
  id: string,
): Promise<any> => {
  const result = await apiCall<any, any>(
    `/backend/api/v1/company/job/edit/${id}/requirements`,
    'GET',
    {
      isAuth: true,
    },
  );

  return result;
};