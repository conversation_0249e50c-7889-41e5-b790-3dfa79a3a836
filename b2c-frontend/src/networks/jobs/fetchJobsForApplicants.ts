import { apiCall } from '@/src/services/api';
import {
  fetchJobsForApplicantBodyI,
  fetchJobsForApplicantQueryI,
  fetchJobsForApplicantsResultI,
} from './types';

export const fetchJobsForApplicantAPI = async (
  query: fetchJobsForApplicantQueryI,
  payload: fetchJobsForApplicantBodyI,
): Promise<fetchJobsForApplicantsResultI> => {
  const result = await apiCall<fetchJobsForApplicantBodyI, fetchJobsForApplicantsResultI>(
    '/backend/api/v1/company/jobs/application/applicant',
    'POST',
    {
      isAuth: true,
      query,
      payload,
    },
  );

  return result;
};
