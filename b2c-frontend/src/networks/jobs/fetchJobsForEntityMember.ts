import { IdTypeI } from '@/src/types/common/data';
import { apiCall } from '@/src/services/api';
import { fetchJobsForApplicantsResultI } from './types';

type FetchJobsForEntityMemberQueryI = {
  isOfficial: boolean;
  status?: string;
  cursorId: string | null;
  pageSize: number;
};

type FetchJobsForEntityMemberBodyI = {
  entity: IdTypeI | null;
  designations?: { id: string; dataType: 'master' | 'raw' }[] | null;
  shipTypes?: string[] | null;
};

export const fetchJobsForEntityMemberAPI = async (
  query: FetchJobsForEntityMemberQueryI,
  payload: FetchJobsForEntityMemberBodyI,
): Promise<fetchJobsForApplicantsResultI> => {
  const result = await apiCall<FetchJobsForEntityMemberBodyI, fetchJobsForApplicantsResultI>(
    '/backend/api/v1/company/job/entity-member',
    'POST',
    {
      isAuth: true,
      query,
      payload,
    },
  );

  return result;
};
