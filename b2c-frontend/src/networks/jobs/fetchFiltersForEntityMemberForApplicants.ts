import { apiCall } from '@/src/services/api';
import { fetchFiltersForEntityMemberForApplicantsQueryI, FiltersForJobsResultI } from './types';

export const fetchFiltersForEntityMemberForApplicantsAPI = async (
  query: fetchFiltersForEntityMemberForApplicantsQueryI,
): Promise<FiltersForJobsResultI> => {
  const result = await apiCall<unknown, FiltersForJobsResultI[]>(
    '/backend/api/v1/company/job/entity-member/filters',
    'GET',
    {
      isAuth: true,
      query,
    },
  );

  return result;
};
