export interface NewsTopicI {
  id: string;
  name: string;
  slug: string;
  description: string;
}

export interface NewsProviderI {
  id: string;
  name: string;
  baseUrl: string;
}

export interface NewsItemI {
  id: string;
  title: string;
  link: string;
  publishedDate: string;
  scrapedAt: string;
  provider: NewsProviderI;
  topics: NewsTopicI[];
}

export interface NewsFetchManyPayloadI {
  cursorId?: string | null;
  pageSize?: string;
  topicId?: string;
  sortBy?: 'publishedDate' | 'scrapedAt';
  sortOrder?: 'asc' | 'desc';
}

export interface NewsFetchManyResultI {
  data: NewsItemI[];
  total: number;
  nextCursorId: string | null;
}

export interface NewsTopicsFetchResultI {
  data: NewsTopicI[];
}
