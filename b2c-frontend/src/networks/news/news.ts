import { apiCall } from '@/src/services/api';
import type { NewsFetchManyPayloadI, NewsFetchManyResultI, NewsTopicsFetchResultI } from './types';

export const fetchNewsAPI = async (query: NewsFetchManyPayloadI): Promise<NewsFetchManyResultI> => {
  const result = await apiCall<unknown, NewsFetchManyResultI>('/backend/api/v1/news', 'GET', {
    isAuth: true,
    query,
  });

  return result;
};

export const fetchNewsTopicsAPI = async (): Promise<NewsTopicsFetchResultI> => {
  const result = await apiCall<unknown, NewsTopicsFetchResultI>(
    '/backend/api/v1/news/topics',
    'GET',
    {
      isAuth: true,
      query: {}, // Add empty query object to satisfy schema
    },
  );

  return result;
};
