import * as React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path } from 'react-native-svg';
import { FilledIconPropsI } from './types';

const CareerFilter: React.FC<FilledIconPropsI> = ({
  width = 4,
  height = 4,
  stroke = '#000000',
  ...props
}) => {
  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 32 32"
      fill="none"
      {...props}
    >
      <Path
        d="M5.332 9.333h21.333M9.332 16h13.333M14.668 22.667h2.667"
        stroke={stroke}
        strokeWidth={3}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
};

export default CareerFilter;
