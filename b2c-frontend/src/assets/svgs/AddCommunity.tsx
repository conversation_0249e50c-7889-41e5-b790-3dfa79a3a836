/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Defs, Path, SvgProps } from 'react-native-svg';
import { FilledIconPropsI } from './types';

const AddCommunityIcon: React.FC<FilledIconPropsI> = ({
  width = 24,
  height = 24,
  fill = '#000',
  color,
  disabled,
  accessibilityLabel = 'Add Community Icon',
  ...props
}) => {
  const strokeColor = '#fff';
  const fillColor = strokeColor;
  const opacity = disabled ? 0.5 : 1;

  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 19200 19200"
      fill="none"
      opacity={opacity}
      accessibilityLabel={accessibilityLabel}
      {...props}
    >
      <Defs></Defs>
      {/* Chat bubble with text lines */}
      <Path
        d="m 0,0 c 0,-6.6 -5.4,-12 -12,-12 h -93.554 c -6.599,0 -12,5.4 -12,12 v 4.886 c 0,6.6 5.401,12 12,12 H -12 c 6.6,0 12,-5.4 12,-12 z m 93.801,-13.642 c -47.428,0 -89.502,22.905 -115.779,58.251 H -106.4 c -6.6,0 -12,5.4 -12,12 v 4.834 c 0,6.6 5.4,12 12,12 h 67.776 c -1.702,3.941 -3.234,7.974 -4.586,12.087 -9.081,1.658 -15.868,6.514 -16.794,12.407 H -106.4 c -6.6,0 -12,5.399 -12,12 v 2.684 c 0,6.6 5.4,12 12,12 h 46.658 v 13.025 h -45.812 c -6.599,0 -12,5.401 -12,12 v 8.884 c 0,6.6 5.401,12 12,12 h 45.812 V 179 h -63.435 c -27.5,0 -50,-22.5 -50,-50 V -13.258 c 0,-24.592 17.997,-45.171 41.468,-49.251 l -3.905,-56.656 81.347,55.907 H 94.187 c 27.5,0 50,22.5 50,50 v 8.673 c -15.686,-5.852 -32.66,-9.057 -50.386,-9.057 z"
        fill="none"
        stroke={strokeColor}
        strokeWidth={6}
        strokeMiterlimit={10}
        transform="matrix(1.3333333,0,0,-1.3333333,11029.719,9688.72)"
      />
      {/* Plus sign - solid fill */}
      <Path
        d="m 0,0 h -39.105 v 39.106 c 0,4.736 -3.876,8.611 -8.612,8.611 -4.737,0 -8.612,-3.875 -8.612,-8.611 V 0 h -39.106 c -4.736,0 -8.612,-3.875 -8.612,-8.612 0,-4.736 3.876,-8.612 8.612,-8.612 h 39.106 V -56.33 c 0,-4.736 3.875,-8.611 8.612,-8.611 4.736,0 8.612,3.875 8.612,8.611 v 39.106 H 0 c 4.737,0 8.612,3.876 8.612,8.612 C 8.612,-3.875 4.737,0 0,0"
        fill={fillColor}
        stroke="none"
        transform="matrix(1.3333333,0,0,-1.3333333,11038.063,9470.0059)"
      />
      {/* People/Community icons */}
      <Path
        d="m 0,0 c 0,2.761 -2.238,5 -5,5 -8.283,0.001 -14.997,6.719 -14.995,15.002 0.003,8.284 6.72,14.998 15.003,14.995 6.837,-10e-4 12.807,-4.626 14.518,-11.246 0.69,-2.674 3.418,-4.283 6.093,-3.593 2.675,0.691 4.283,3.419 3.593,6.093 -3.454,13.367 -17.089,21.404 -30.457,17.95 -13.367,-3.453 -21.403,-17.089 -17.949,-30.456 1.21,-4.685 3.754,-8.918 7.322,-12.185 -6.792,-2.947 -12.695,-7.619 -17.125,-13.553 -1.655,-2.21 -1.205,-5.343 1.005,-6.998 2.208,-1.654 5.338,-1.207 6.994,0.999 6.111,8.215 15.76,13.039 25.998,12.999 2.762,0 5,2.238 5,4.999 z m 34.998,-34.998 c 11.046,0 20.002,8.956 20.002,20.002 0,11.047 -8.956,20.002 -20.002,20.002 -11.045,0 -19.999,-8.953 -19.999,-19.998 0,-11.045 8.954,-19.999 19.999,-19.999 z m 39.323,-22.498 c 1.497,-2.32 0.829,-5.415 -1.492,-6.911 -2.32,-1.497 -5.414,-0.828 -6.911,1.492 -0.088,0.136 -0.168,0.276 -0.243,0.419 -10.005,16.942 -31.848,22.565 -48.789,12.56 -5.18,-3.059 -9.501,-7.38 -12.559,-12.56 -1.265,-2.454 -4.28,-3.419 -6.735,-2.154 -2.454,1.265 -3.419,4.28 -2.154,6.734 0.074,0.144 0.155,0.284 0.243,0.42 4.846,8.329 12.236,14.883 21.085,18.699 C 3.609,-28.728 1.107,-9.9 11.176,3.256 c 10.069,13.157 28.897,15.659 42.053,5.59 13.157,-10.07 15.659,-28.897 5.59,-42.054 -1.609,-2.101 -3.488,-3.981 -5.59,-5.589 8.851,-3.815 16.244,-10.37 21.092,-18.699 z m 33.671,38.497 c -2.212,-1.645 -5.337,-1.198 -6.999,1 C 94.881,-9.785 85.232,-4.96 74.995,-5 c -2.762,0.143 -4.885,2.497 -4.742,5.258 0.133,2.562 2.18,4.609 4.742,4.742 8.283,-0.002 15,6.712 15.001,14.996 0.002,8.283 -6.712,15 -14.996,15.001 -6.839,0.002 -12.813,-4.624 -14.524,-11.246 -0.691,-2.676 -3.42,-4.286 -6.097,-3.596 -2.676,0.69 -4.286,3.42 -3.596,6.096 3.458,13.366 17.097,21.398 30.463,17.94 13.366,-3.458 21.398,-17.097 17.94,-30.462 -1.21,-4.678 -3.75,-8.905 -7.313,-12.169 6.797,-2.945 12.706,-7.618 17.139,-13.553 1.652,-2.213 1.196,-5.345 -1.017,-6.997 -0.001,0 -0.002,-0.001 -0.003,-0.002 z"
        fill="none"
        stroke={strokeColor}
        strokeWidth={6}
        strokeMiterlimit={10}
        transform="matrix(1.3333333,0,0,-1.3333333,11117.193,9498.0651)"
      />
      {/* Question mark circle */}
      <Path
        d="m 0,0 c -53.606,0 -99.069,-34.836 -115.004,-83.103 h 33.789 c 6.6,0 12,-5.4 12,-12 v -9.005 c 0,-6.6 -5.4,-12 -12,-12 h -39.766 c -0.067,-1.653 -0.11,-3.313 -0.11,-4.983 0,-66.876 54.214,-121.091 121.091,-121.091 66.876,0 121.09,54.215 121.09,121.091 C 121.09,-54.214 66.876,0 0,0 Z"
        fill="none"
        stroke={strokeColor}
        strokeWidth={6}
        strokeMiterlimit={10}
        transform="matrix(1.3333333,0,0,-1.3333333,11156.46,9350.0801)"
      />
    </Svg>
  );
};

export default AddCommunityIcon;
