import type { CareerStackParamListI, StackScreenI } from '@/src/navigation/types';
import ApplicantsScreen from '@/src/screens/Applicants';
import CareerDetailedScreen from '@/src/screens/CareerDetailed';
import MyJobsScreen from '@/src/screens/Careers/components/MyJobs';
import JobPostsScreen from '@/src/screens/JobPosts';
import { withErrorBoundary } from '@/src/hocs/withErrorBoundary';
import CreateExternalJobPostScreen from '@/src/screens/CreateExternalJobPost';
import CareersScreen from '@/src/screens/Careers';
import EditJobPostScreen from '@/src/screens/EditJobPost';
import EntitySearchScreen from '@/src/screens/EntitySearch';

const createErrorBoundaryScreen = (Component: any, title: string, subtitle: string) =>
  withErrorBoundary(Component, { title, subtitle });

const screenConfigs = [
  {
    name: 'Careers',
    component: CareersScreen,
    title: 'Careers Error',
    subtitle: 'Something went wrong loading careers. Please try again.',
  },
  {
    name: '<PERSON><PERSON><PERSON>s',
    component: MyJobsScreen,
    title: 'Jobs Error',
    subtitle: 'Something went wrong loading jobs. Please try again.',
  },
  {
    name: 'JobPosts',
    component: JobPostsScreen,
    title: 'Job Posts Error',
    subtitle: 'Something went wrong loading job posts. Please try again.',
  },
  {
    name: 'CareerDetails',
    component: CareerDetailedScreen,
    title: 'Career Details Error',
    subtitle: 'Something went wrong loading career details. Please try again.',
  },
  {
    name: 'Applicants',
    component: ApplicantsScreen,
    title: 'Applicants Error',
    subtitle: 'Something went wrong loading applicants. Please try again.',
  },
  {
    name: 'EditJobPost',
    component: EditJobPostScreen,
    title: 'Job Posting Error',
    subtitle: 'Something went wrong loading Job Posting. Please try again.',
  },
  {
    name: 'CreateExternalJobPost',
    component: CreateExternalJobPostScreen,
    title: 'Create Screen Error',
    subtitle: 'Something went wrong loading screen. Please try again.',
  },
  {
    name: 'SearchScreen',
    component: EntitySearchScreen,
    title: 'Search Error',
    subtitle: 'Something went wrong during search. Please try again.',
  },
];

export const screens: StackScreenI<CareerStackParamListI>[] = screenConfigs.map(
  ({ name, component, title, subtitle }) => ({
    name: name as keyof CareerStackParamListI,
    component: createErrorBoundaryScreen(component, title, subtitle),
  }),
);
