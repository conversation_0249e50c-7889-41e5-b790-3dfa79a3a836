import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { AIStackParamListI } from '../../types';
import { screens } from './screen';

const AIStack = createStackNavigator<AIStackParamListI>();

const AIStackNavigator = (): React.JSX.Element => {
  return (
    <AIStack.Navigator
      screenOptions={{
        headerShown: false,
        animation: 'fade',
        cardStyle: { backgroundColor: 'white' },
      }}
      initialRouteName="AIAssistant"
    >
      {screens.map(({ name, component }) => (
        <AIStack.Screen key={name} name={name} component={component} />
      ))}
    </AIStack.Navigator>
  );
};

export default AIStackNavigator;
