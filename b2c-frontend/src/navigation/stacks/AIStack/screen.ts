import AIChatScreen from '@/src/screens/AIChat';
import { withErrorBoundary } from '../../../hocs/withErrorBoundary';
import { AIStackParamListI, StackScreenI } from '../../types';

const AIAssistantScreenWithErrorBoundary = withErrorBoundary(AIChatScreen, {
  title: 'AI Assistant Error',
  subtitle: 'Something went wrong while loading AI Assistant. Please try again.',
});

export const screens: StackScreenI<AIStackParamListI>[] = [
  { name: 'AIAssistant', component: AIAssistantScreenWithErrorBoundary },
];
