import type React from 'react';
import { useRef, useEffect } from 'react';
import { Pressable, Animated, Image, View } from 'react-native';
import {
  createBottomTabNavigator,
  type BottomTabBarProps,
  type BottomTabBarButtonProps,
} from '@react-navigation/bottom-tabs';
import {
  getFocusedRouteNameFromRoute,
  useNavigation,
  useNavigationState,
} from '@react-navigation/native';
import { useSelector } from 'react-redux';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import HomeStackNavigator from '@/src/navigation/stacks/HomeStack';
import NotificationStackNavigator from '@/src/navigation/stacks/NotificationStack';
import AiIcon from '@/src/assets/images/others/aibot.png';
import HomeIcon from '@/src/assets/svgs/HomeTab';
import Job from '@/src/assets/svgs/Job';
import LearnAndCollabIcon from '@/src/assets/svgs/LearnCollabTab';
import Location from '@/src/assets/svgs/Location';
import NotificationIcon from '@/src/assets/svgs/NotificationTab';
import AIStackNavigator from '../stacks/AIStack';
import CareerStackNavigator from '../stacks/CareerStack';
import LearnCollabStackNavigator from '../stacks/LearnCollabStack';
import NearbyStackNavigator from '../stacks/NearbyStack';
import type { HomeScreenActionsRef } from '../types';

const Tab = createBottomTabNavigator();

const TabButton = (props: BottomTabBarButtonProps) => {
  const scale = useRef(new Animated.Value(1)).current;
  const handlePressIn = () =>
    Animated.spring(scale, { toValue: 0.95, useNativeDriver: true }).start();
  const handlePressOut = () =>
    Animated.spring(scale, { toValue: 1, useNativeDriver: true }).start();
  return (
    <Pressable
      onPress={props.onPress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      style={props.style}
      accessibilityRole={props.accessibilityRole}
    >
      <Animated.View style={{ transform: [{ scale }] }}>{props.children}</Animated.View>
    </Pressable>
  );
};

const CustomTabBar = ({ state, descriptors, navigation }: BottomTabBarProps) => {
  const translateY = useRef(new Animated.Value(0)).current;
  const currentRoute = state.routes[state.index];
  const currentOptions = descriptors[currentRoute.key]?.options;
  const shouldHide =
    currentOptions?.tabBarStyle &&
    typeof currentOptions.tabBarStyle === 'object' &&
    'display' in currentOptions.tabBarStyle &&
    currentOptions.tabBarStyle.display === 'none';
  useEffect(() => {
    Animated.timing(translateY, {
      toValue: shouldHide ? 100 : 0,
      duration: 200,
      useNativeDriver: true,
    }).start();
  }, [shouldHide, translateY]);
  return (
    <Animated.View
      style={{
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        backgroundColor: '#ffffff',
        height: 80,
        flexDirection: 'row',
        paddingHorizontal: 20,
        paddingBottom: 25,
        paddingTop: 15,
        borderTopWidth: 1,
        borderTopColor: '#f0f0f0',
        borderRadius: 20,
        transform: [{ translateY }],
      }}
    >
      {state.routes.map((route, index) => {
        const { options } = descriptors[route.key];
        const isFocused = state.index === index;
        const onPress = () => {
          const event = navigation.emit({
            type: 'tabPress',
            target: route.key,
            canPreventDefault: true,
          });
          if (!isFocused && !event.defaultPrevented) navigation.navigate(route.name, route.params);
        };
        return (
          <View key={route.key} style={{ flex: 1, alignItems: 'center', justifyContent: 'center' }}>
            <TabButton
              onPress={onPress}
              style={{ flex: 1, alignItems: 'center', justifyContent: 'center' }}
            >
              {options.tabBarIcon?.({ focused: isFocused, color: '', size: 0 })}
            </TabButton>
          </View>
        );
      })}
    </Animated.View>
  );
};

const TabIcon = ({
  IconComponent,
  focused,
  routeName,
}: {
  IconComponent?: React.ComponentType<{ fill: string; width?: number; height?: number }>;
  focused: boolean;
  routeName: string;
}) => {
  const backgroundColor = focused ? '#DDEFC8' : 'transparent';
  const fillColor =
    routeName === 'HomeStack' ? (focused ? '#006400' : '#000000') : focused ? '#448600' : '#000000';
  return (
    <View
      style={{
        alignItems: 'center',
        justifyContent: 'center',
        paddingHorizontal: 16,
        paddingVertical: 8,
        borderRadius: 20,
        backgroundColor,
      }}
    >
      {IconComponent ? (
        <IconComponent fill={fillColor} width={2.34} height={2.34} />
      ) : (
        <Image source={AiIcon} style={{ width: 23, height: 23 }} resizeMode="contain" />
      )}
    </View>
  );
};

const hiddenScreens = {
  home: [
    'Chat',
    'Chats',
    'AIChat',
    'GlobalSearch',
    'Comment',
    'OtherUserProfile',
    'PortProfile',
    'ShipProfile',
    'EditDocumentList',
    'EditCertificationList',
    'EditEducationList',
    'EditSkillsList',
    'EditExperienceList',
    'Connection',
    'Likes',
    'EditShipItem',
    'PortsVisited',
    'UserSettings',
    'ReferralDetails',
    'Leaderboard',
  ],
  learnCollab: ['ForumAnswers', 'CreateQuestion', 'ForumComments', 'SearchScreen'],
  notification: ['ForumAnswers', 'ForumComments', 'Like', 'Comments', 'OtherUserProfile'],
  nearby: [],
  hideFAB: ['AIStack', 'ReferralDetails'],
};

const getTabOptions = (route: any, screens: string[]) => {
  const routeName = getFocusedRouteNameFromRoute(route) ?? 'Home';
  return screens.includes(routeName) ? { tabBarStyle: { display: 'none' as const } } : {};
};

const BottomTabNavigator = () => {
  const homeScreenActionsRef = useRef<HomeScreenActionsRef['current']>({});
  const isUserActive = useSelector(selectCurrentUser).isActive;

  return (
    <>
      <Tab.Navigator
        screenOptions={{ headerShown: false, tabBarShowLabel: false, tabBarButton: TabButton }}
        tabBar={(props) => <CustomTabBar {...props} />}
      >
        <Tab.Screen
          name="HomeStack"
          options={({ route }) => ({
            tabBarIcon: ({ focused }) => (
              <TabIcon IconComponent={HomeIcon} focused={focused} routeName="HomeStack" />
            ),
            ...getTabOptions(route, hiddenScreens.home),
          })}
        >
          {() => <HomeStackNavigator homeScreenActionsRef={homeScreenActionsRef} />}
        </Tab.Screen>
        {isUserActive && (
          <Tab.Screen
            name="LearnCollabStack"
            component={LearnCollabStackNavigator}
            options={({ route }) => ({
              tabBarIcon: ({ focused }) => (
                <TabIcon
                  IconComponent={LearnAndCollabIcon}
                  focused={focused}
                  routeName="LearnCollabStack"
                />
              ),
              ...getTabOptions(route, hiddenScreens.learnCollab),
            })}
          />
        )}
        <Tab.Screen
          name="CareerStack"
          component={CareerStackNavigator}
          options={({ route }) => ({
            tabBarIcon: ({ focused }) => (
              <TabIcon IconComponent={Job} focused={focused} routeName="CareerStack" />
            ),
            ...getTabOptions(route, hiddenScreens.nearby),
          })}
          listeners={({ navigation }) => ({
            tabPress: (e) => {
              e.preventDefault();
              navigation.navigate('CareerStack', { screen: isUserActive ? 'Careers' : 'JobPosts' });
            },
          })}
        />
        <Tab.Screen
          name="NotificationStack"
          component={NotificationStackNavigator}
          options={({ route }) => ({
            tabBarIcon: ({ focused }) => (
              <TabIcon
                IconComponent={NotificationIcon}
                focused={focused}
                routeName="NotificationStack"
              />
            ),
            ...getTabOptions(route, hiddenScreens.notification),
          })}
        />
        <Tab.Screen
          name="NearbyStack"
          component={NearbyStackNavigator}
          options={({ route }) => ({
            tabBarIcon: ({ focused }) => (
              <TabIcon IconComponent={Location} focused={focused} routeName="NearbyStack" />
            ),
            ...getTabOptions(route, hiddenScreens.nearby),
          })}
        />
        <Tab.Screen
          name="AIStack"
          component={AIStackNavigator}
          options={{
            tabBarIcon: ({ focused }) => <TabIcon focused={focused} routeName="AIStack" />,
          }}
        />
      </Tab.Navigator>
    </>
  );
};

export default BottomTabNavigator;
