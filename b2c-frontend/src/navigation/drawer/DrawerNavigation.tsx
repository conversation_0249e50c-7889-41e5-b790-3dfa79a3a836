import { useState, useMemo, useEffect } from 'react';
import { Text, View, ScrollView, Pressable } from 'react-native';
import { createDrawerNavigator, DrawerItemList } from '@react-navigation/drawer';
import { CommonActions } from '@react-navigation/native';
import LinearGradient from 'react-native-linear-gradient';
import { useDispatch, useSelector } from 'react-redux';
import SafeArea from '@/src/components/SafeArea';
import SwitchProfileModal from '@/src/components/SwitchProfile';
import type { SwitchProfileTypeI } from '@/src/components/SwitchProfile/types';
import UserAvatar from '@/src/components/UserAvatar';
import {
  selectCurrentEntityProfile,
  selectEntityProfilesList,
} from '@/src/redux/selectors/entityProfile';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { showToast } from '@/src/utilities/toast';
import ProfileStackNavigator from '@/src/navigation/stacks/ProfileStack';
import BottomTabNavigator from '@/src/navigation/tabs/TabNavigation';
import ScoreboardScreen from '@/src/screens/Scoreboard';
import AddItem from '@/src/assets/svgs/AddItem';
import ChevronRight from '@/src/assets/svgs/ChevronRight';
import Invite from '@/src/assets/svgs/Invite';
import { fetchEntityProfilesForUserAPI } from '@/src/networks/entityProfile/fetchEntityProfilesForUser';
import CreateStackNavigator from '../stacks/CreateStack';
import HomeStackNavigator from '../stacks/HomeStack';
import LearnCollabStackNavigator from '../stacks/LearnCollabStack';
import { AppDispatch } from '@/src/redux/store';
import { fetchEntityProfiles } from '@/src/redux/slices/entityprofile/entityProfileListSlice';

const Drawer = createDrawerNavigator();

const CustomDrawerContent = (props: any) => {
  const currentUser = useSelector(selectCurrentUser);
  const currentEntity = useSelector(selectCurrentEntityProfile);
  const { items } = useSelector(selectEntityProfilesList);
  const isUserActive = currentUser.isActive;
  const userDesignation = currentUser?.designation?.name || 'Not specified';
  const userOrganisation = currentUser?.organisation?.name;
  const [isModalVisible, setIsModalVisible] = useState(false);
  const dispatch = useDispatch<AppDispatch>();

  const cachedProfiles = useMemo<SwitchProfileTypeI[]>(() => {
    const userProfile = {
      id: currentUser.profileId,
      name: currentUser.fullName,
      avatar: currentUser.avatar,
      type: 'USER' as const,
      role: undefined,
      isVerified: undefined,
    };
    const entityProfiles = items.map((p) => ({
      id: p.id,
      name: p.name,
      avatar: p.avatar,
      type: 'ENTITY' as const,
      role: p.role,
      isVerified: p.isVerified,
    }));
    return [userProfile, ...entityProfiles];
  }, [items, currentUser]);

  const handleSwitchProfilePress = () => {
    dispatch(fetchEntityProfiles());
    setIsModalVisible(true);
    props.navigation.closeDrawer();
  };

  const handleInviteFriendPress = () => {
    props.navigation.navigate('MainTabs', {
      screen: 'HomeStack',
      params: { screen: 'ReferralDetails' },
    });
  };

  const handleProfilePress = () => {
    props.navigation.dispatch(
      CommonActions.reset({
        index: 0,
        routes: [
          {
            name: 'ProfileStack',
            state: {
              routes: [
                { name: 'UserProfile', params: { profileId: undefined, fromTabPress: true } },
              ],
            },
          },
        ],
      }),
    );
  };

  const handleEntityProfilePress = () => {
    props.navigation.navigate('MainTabs', {
      screen: 'HomeStack',
      params: { screen: 'EntityProfile', params: { entityProfileId: undefined } },
    });
  };

  const modalNavigation = props.navigation;

  const UserDrawer = () => {
    return (
      <>
        <View className="px-6 pt-10 pb-6 border-b border-gray-200">
          <View className="items-start gap-4">
            <Pressable android_ripple={null} onPress={handleProfilePress}>
              <UserAvatar
                avatarUri={currentUser?.avatar}
                name={currentUser?.fullName}
                width={88}
                height={88}
                className="-ml-3"
              />
            </Pressable>
            <View className="gap-1">
              <Text className="text-2xl font-bold text-gray-900">
                {currentUser?.fullName || 'Current'}
              </Text>
              <Text className="text-sm text-gray-500 leading-5" numberOfLines={2}>
                {`${userDesignation}${userOrganisation ? ` at ${userOrganisation}` : ``}`}
              </Text>
            </View>
          </View>
        </View>

        <View className="mt-6">
          <DrawerItemList {...props} isUserActive={true} />
        </View>

        <View className="px-4 mt-6">
          <View className="rounded-2xl overflow-hidden">
            <LinearGradient
              colors={['#FFD400', '#FFC56B']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              style={{ borderRadius: 16 }}
            >
              <Pressable android_ripple={null} className="p-5" onPress={handleInviteFriendPress}>
                <View className="flex-row items-start gap-3">
                  <Invite />
                  <View className="flex-1 gap-1">
                    <Text className="text-lg font-semibold text-gray-900 leading-6">
                      Invite a friend!
                    </Text>
                    <Text className="text-sm text-gray-800 leading-5">
                      Share Navicater, earn rewards
                    </Text>
                    <Text className="text-xs text-gray-700 leading-4">10 points per referral</Text>
                  </View>
                </View>
              </Pressable>
            </LinearGradient>
          </View>
        </View>

        <View className="mt-6 border-t border-gray-200" />

        <View className="pt-3 gap-6">
          <Pressable
            android_ripple={null}
            onPress={() => props.navigation.navigate('Settings')}
            className="px-6 py-3"
          >
            <Text
              style={{
                fontFamily: 'Inter',
                fontWeight: '500',
                fontSize: 16,
                lineHeight: 16,
                letterSpacing: 0,
                color: '#1F2937',
              }}
            >
              Settings
            </Text>
          </Pressable>

          {/* <Pressable android_ripple={null} className="px-6">
            <Text
              style={{
                fontFamily: 'Inter',
                fontWeight: '500',
                fontSize: 16,
                lineHeight: 16,
                letterSpacing: 0,
                color: '#1F2937',
              }}
            >
              Help and support
            </Text>
          </Pressable> */}

          <Pressable
            android_ripple={null}
            onPress={() =>
              props.navigation.navigate('MainTabs', {
                screen: 'HomeStack',
                params: {
                  screen: 'CreateEntityProfile',
                },
              })
            }
            className="flex-row items-center gap-3 px-6 pb-3"
          >
            <AddItem color="#22A000" />
            <Text
              style={{
                fontFamily: 'Inter',
                fontWeight: '500',
                fontSize: 16,
                lineHeight: 16,
                letterSpacing: 0,
                color: '#15803D',
              }}
            >
              Create company profile
            </Text>
          </Pressable>
        </View>
      </>
    );
  };

  const EntityDrawer = () => {
    return (
      <>
        <View className="px-6 pt-10 pb-6 border-b border-gray-200">
          <View className="items-start gap-4">
            <Pressable android_ripple={null} onPress={handleEntityProfilePress}>
              <UserAvatar
                avatarUri={currentEntity?.avatar}
                name={currentEntity?.name}
                width={88}
                height={88}
                className="border-4 border-green-800/20 rounded-full -ml-3"
              />
            </Pressable>
            <Text className="text-2xl font-bold text-gray-900">
              {currentEntity?.name || 'Current Company'}
            </Text>
          </View>
        </View>
        <View className="mt-6">
          <DrawerItemList {...props} isUserActive={false} />
        </View>
      </>
    );
  };

  return (
    <SafeArea>
      <View className="flex-1 bg-white">
        <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
          {isUserActive ? <UserDrawer /> : <EntityDrawer />}
        </ScrollView>

        <View className="border-t border-gray-200 bg-white">
          <Pressable
            android_ripple={null}
            onPress={handleSwitchProfilePress}
            className="flex-row items-center px-6 pb-12 pt-6 justify-between"
          >
            <Text
              style={{
                fontFamily: 'Inter',
                fontWeight: '500',
                fontSize: 16,
                lineHeight: 16,
                letterSpacing: 0,
                color: '#1F2937',
              }}
            >
              Switch profile
            </Text>
            <ChevronRight width={2.3} height={2.3} color="#000000" />
          </Pressable>
        </View>
      </View>
      <SwitchProfileModal
        visible={isModalVisible}
        onClose={() => setIsModalVisible(false)}
        profiles={cachedProfiles}
        modalNavigation={modalNavigation}
      />
    </SafeArea>
  );
};

const DrawerNavigator = () => {
  const isUserActive = useSelector(selectCurrentUser).isActive;
  return (
    <Drawer.Navigator
      screenOptions={{
        headerShown: false,
        drawerStyle: {
          width: '80%',
          backgroundColor: '#ffffff',
        },
        drawerLabelStyle: {
          fontFamily: 'Inter',
          fontWeight: '500',
          fontSize: 16,
          lineHeight: 16,
          letterSpacing: 0,
          color: '#1F2937',
          marginLeft: -8,
        },
        drawerActiveBackgroundColor: 'transparent',
        drawerInactiveBackgroundColor: 'transparent',
        drawerItemStyle: {
          marginHorizontal: 16,
        },
      }}
      drawerContent={(props) => <CustomDrawerContent {...props} />}
    >
      <Drawer.Screen
        name="MainTabs"
        component={BottomTabNavigator}
        options={{
          title: 'Home',
          drawerItemStyle: { display: 'none' },
        }}
      />
      {isUserActive ? (
        <>
          <Drawer.Screen
            name="ProfileStack"
            component={ProfileStackNavigator}
            options={{
              title: 'My profile',
            }}
            initialParams={{
              screen: 'UserProfile',
              params: { profileId: undefined, fromTabPress: true },
            }}
          />
          <Drawer.Screen
            name="SavedPosts"
            component={HomeStackNavigator}
            options={{
              title: 'Saved posts',
            }}
            initialParams={{
              screen: 'SavedPosts',
            }}
          />
          <Drawer.Screen
            name="Settings"
            component={HomeStackNavigator}
            options={{
              title: 'Settings',
              drawerItemStyle: { display: 'none' },
            }}
            initialParams={{
              screen: 'UserSettings',
            }}
          />
          <Drawer.Screen
            name="My Scoreboard"
            component={ScoreboardScreen}
            options={{ title: 'My scoreboard' }}
          />
          <Drawer.Screen
            name="CreateStack"
            component={CreateStackNavigator}
            options={{
              drawerItemStyle: { display: 'none' },
            }}
          />
        </>
      ) : (
        <>
          <Drawer.Screen
            name="Profile"
            component={HomeStackNavigator}
            options={{
              title: 'Organization Profile',
            }}
            initialParams={{
              screen: 'EntityProfile',
              params: { entityProfileId: undefined },
            }}
          />
        </>
      )}
    </Drawer.Navigator>
  );
};

export default DrawerNavigator;
