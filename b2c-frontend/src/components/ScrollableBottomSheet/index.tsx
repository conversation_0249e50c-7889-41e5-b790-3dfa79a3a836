import type React from 'react';
import { View, ScrollView } from 'react-native';
import Modal from 'react-native-modal';
import { ScrollableBottomSheetProps } from './types';

const ScrollableBottomSheet: React.FC<ScrollableBottomSheetProps> = ({ 
  visible, 
  onClose, 
  onModalHide, 
  children,
  height = 200
}) => {
  return (
    <View>
      <Modal
        isVisible={visible}
        onBackdropPress={onClose}
        onBackButtonPress={onClose}
        onModalHide={onModalHide}
        style={{
          justifyContent: 'flex-end',
          margin: 0,
        }}
        animationIn="slideInUp"
        animationOut="slideOutDown"
        backdropOpacity={0.5}
        backdropTransitionOutTiming={1}
        backdropTransitionInTiming={250}
        backdropColor="#000"
        useNativeDriver={true}
        hideModalContentWhileAnimating={true}
      >
        <View style={{ 
          backgroundColor: 'white', 
          borderTopLeftRadius: 20, 
          borderTopRightRadius: 20,
          maxHeight:400
        }}>
          <ScrollView 
            style={{ maxHeight: '100%' }}
            showsVerticalScrollIndicator={false}
          >
            <View style={{ padding: 20 }}>
              {children}
            </View>
          </ScrollView>
        </View>
      </Modal>
    </View>
  );
};

export default ScrollableBottomSheet;