import { useRef, useEffect } from 'react';
import { Pressable, Animated } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import AddCommunityQuestionIcon from '@/src/assets/svgs/AddCommunityQuestion';
import CreateQuestionIcon from '@/src/assets/svgs/CreateQuestion';
import Plus from '@/src/assets/svgs/Plus';
import type { FloatingActionButtonProps, FloatingButtonIconProps } from './types';

const FloatingButtonIcon = ({ context }: FloatingButtonIconProps) => {
  switch (context) {
    case 'CREATE_POST':
      return <Plus fill="#FFFFFF" />;
    case 'CREATE_QUESTION':
      return <CreateQuestionIcon fill="#FFFFFF" />;
    case 'CREATE_EVENT':
      return <Plus fill="#FFFFFF" />;
    case 'CREATE_COMMUNITY':
      return <AddCommunityQuestionIcon fill="#FFFFFF" />;
    case 'CREATE_COMMUNITY_QUESTION':
      return <CreateQuestionIcon fill="#FFFFFF" />;
    default:
      return <Plus fill="#FFFFFF" />;
  }
};

const FloatingActionButton = ({ context, onPress, visible = true }: FloatingActionButtonProps) => {
  const scale = useRef(new Animated.Value(1)).current;
  const opacity = useRef(new Animated.Value(visible ? 1 : 0)).current;
  const insets = useSafeAreaInsets();

  useEffect(() => {
    Animated.timing(opacity, {
      toValue: visible ? 1 : 0,
      duration: 200,
      useNativeDriver: true,
    }).start();
  }, [visible, opacity]);

  const handlePressIn = () => {
    Animated.spring(scale, { toValue: 0.9, useNativeDriver: true }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scale, { toValue: 1, useNativeDriver: true }).start();
  };

  if (!visible) {
    return null;
  }

  return (
    <Animated.View
      style={{
        position: 'absolute',
        bottom: 60 + insets.bottom,
        right: 15,
        zIndex: 1000,
        opacity,
        transform: [{ scale }],
      }}
    >
      <Pressable
        onPress={onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        style={{
          width: 56,
          height: 56,
          borderRadius: 28,
          backgroundColor: '#448600',
          alignItems: 'center',
          justifyContent: 'center',
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 0.3,
          shadowRadius: 8,
          elevation: 8,
        }}
      >
        <FloatingButtonIcon context={context} />
      </Pressable>
    </Animated.View>
  );
};

export default FloatingActionButton;
