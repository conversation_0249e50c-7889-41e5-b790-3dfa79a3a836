/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { Dispatch, SetStateAction } from 'react';

export type Tab = {
  id: string;
  label: string;
  appliedCount: number;
};

export type FilterTabsProps<T = string> = {
  tabs: Tab[];
  activeTab: T;
  onTabChange: Dispatch<SetStateAction<T>>;
  styles?: string;
  disabled?: boolean;
};

export type FilterTabItemProps = {
  tab: Tab;
  isActive: boolean;
  onPress: () => void;
  styles?: string;
  disabled?: boolean;
};
