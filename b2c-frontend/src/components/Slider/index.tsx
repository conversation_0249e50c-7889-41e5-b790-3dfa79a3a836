import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Slider from '@react-native-community/slider';

interface CustomSliderProps {
  label: string;
  min?: number;
  max?: number;
  step?: number;
  initialValue?: number;
  value?: number;
  onChange?: (value: number) => void;
  trackColor?: string;
  thumbColor?: string;
  valueFormatter?: (value: number) => string;
}

const CustomSlider: React.FC<CustomSliderProps> = ({
  label,
  min = 0,
  max = 1,
  step = 0.001,
  initialValue = min,
  value,
  onChange,
  trackColor = '#448600',
  thumbColor = '#448600',
  valueFormatter = (val) => val.toFixed(2),
}) => {
  const [internalValue, setInternalValue] = React.useState(initialValue);
  const isControlled = value !== undefined;
  const currentValue = isControlled ? value! : internalValue;

  const handleChange = (val: number) => {
    if (!isControlled) setInternalValue(val);
    onChange?.(val);
  };

  return (
    <View className='my-4'>
      <View className='flex-row items-center justify-between mb-2'>
        <Text className='font-medium text-lg leading-snug text-[#212121]'>{label}</Text>
        <Text className='bg-[#F3F4F6] px-3 py-2 rounded-2xl text-[#4B5563]'>{valueFormatter(currentValue)}</Text>
      </View>

      <Slider
        minimumValue={min}
        maximumValue={max}
        step={step}
        value={currentValue}
        onValueChange={handleChange}
        minimumTrackTintColor={trackColor}
        maximumTrackTintColor="#E5E7EB"
        thumbTintColor={thumbColor}
        />

      <View className='flex-row justify-between mt-2'>
        <Text className='text-[#6B7280]'>{valueFormatter(min)}</Text>
        <Text className='text-[#6B7280]'>{valueFormatter(max)}</Text>
      </View>
    </View>
  );
};

export default CustomSlider;
