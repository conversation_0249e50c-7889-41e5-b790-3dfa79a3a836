name: 'Build Android App'
description: 'Builds an Android AAB'

inputs:
  version:
    description: 'Semantic version'
    required: true
  build-number:
    description: 'Build number'
    required: true
  keystore-password:
    description: 'Keystore password'
    required: true
  keystore-alias:
    description: 'Keystore alias'
    required: true
  key-password:
    description: 'Key password'
    required: true
  keystore-base64:
    description: 'Base64-encoded keystore'
    required: true

runs:
  using: 'composite'
  steps:
    - name: Assert required inputs
      shell: bash
      run: |
        set -euo pipefail
        [ -n "${{ inputs.version }}" ] || { echo "❌ 'version' input is required"; exit 1; }
        [ -n "${{ inputs['build-number'] }}" ] || { echo "❌ 'build-number' input is required"; exit 1; }

    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '20'
        cache: 'npm'
        cache-dependency-path: 'package-lock.json'

    - name: Install Node.js Dependencies
      shell: bash
      run: |
        echo "📦 Installing Node.js dependencies"
        npm ci

    - name: Assert JS deps present before Gradle
      shell: bash
      run: |
        set -e
        test -d node_modules
        test -f node_modules/@react-native/gradle-plugin/package.json
        echo "✅ node_modules and RN gradle plugin present"

    - name: Action env
      shell: bash
      run: |
        echo "BASE_URL: ${{ env.BASE_URL }}"

    - name: Create .env file
      shell: bash
      run: |
        echo "🔐 Creating .env file"
        cat > .env <<EOF
        API_KEY=${{ env.API_KEY }}
        BASE_URL=${{ env.BASE_URL }}
        AI_URL=${{ env.AI_URL }}
        FIREBASE_ANDROID_APP_ID=${{ env.FIREBASE_ANDROID_APP_ID }}
        FIREBASE_API_KEY=${{ env.FIREBASE_API_KEY }}
        FIREBASE_AUTH_DOMAIN=${{ env.FIREBASE_AUTH_DOMAIN }}
        FIREBASE_DATABASE_URL=${{ env.FIREBASE_DATABASE_URL }}
        FIREBASE_IOS_APP_ID=${{ env.FIREBASE_IOS_APP_ID }}
        FIREBASE_MESSAGING_SENDER_ID=${{ env.FIREBASE_MESSAGING_SENDER_ID }}
        FIREBASE_PROJECT_ID=${{ env.FIREBASE_PROJECT_ID }}
        FIREBASE_STORAGE_BUCKET=${{ env.FIREBASE_STORAGE_BUCKET }}
        IOS_CLIENT_ID=${{ env.IOS_CLIENT_ID }}
        SENTRY_DSN_URL=${{ env.SENTRY_DSN_URL }}
        WEB_CLIENT_ID=${{ env.WEB_CLIENT_ID }}
        ENV=${{ env.ENV }}
        MAPBOX_ACCESS_TOKEN=${{ env.MAPBOX_ACCESS_TOKEN }}
        EOF

    - name: Set up JDK
      uses: actions/setup-java@v4
      with:
        java-version: '17'
        distribution: 'temurin'
        cache: 'gradle'

    - name: Set up Android SDK
      uses: android-actions/setup-android@v3

    - name: Install Android SDK packages
      shell: bash
      run: |
        set -e
        COMPILE_SDK=$(grep -E '^COMPILE_SDK=' android/gradle.properties | cut -d= -f2 | tr -d '[:space:]')
        BUILD_TOOLS=$(grep -E '^BUILD_TOOLS=' android/gradle.properties | cut -d= -f2 | tr -d '[:space:]')

        echo "Accepting Android SDK licenses"
        yes | sdkmanager --licenses >/dev/null || true

        echo "Installing Android SDK platform ${COMPILE_SDK} and build-tools ${BUILD_TOOLS}"
        sdkmanager "platforms;android-${COMPILE_SDK}" "build-tools;${BUILD_TOOLS}" "platform-tools"

        echo "sdkmanager version:"
        sdkmanager --version

    - name: Assert Gradle codegen wiring present
      shell: bash
      run: |
        set -e
        grep -q "gradle.projectsEvaluated" android/app/build.gradle
        grep -q "generateCodegenArtifactsFromSchema" android/app/build.gradle
        echo "✅ Codegen wiring present in android/app/build.gradle"

    - name: Decode Keystore
      shell: bash
      run: |
        echo "🔓 Decoding keystore"
        echo "${KEYSTORE_BASE64}" | base64 --decode > android/app/release.keystore
        ls -l android/app/release.keystore || { echo "❌ Keystore file missing!"; exit 1; }
      env:
        KEYSTORE_BASE64: ${{ inputs.keystore-base64 }}

    - name: Inject VERSION_* for all Gradle runs
      shell: bash
      run: |
        set -e
        { 
          echo "VERSION_CODE=${{ inputs.build-number }}"; 
          echo "VERSION_NAME=${{ inputs.version }}"; 
        } | tee -a android/gradle.properties
        echo "→ android/gradle.properties now has:"
        grep -E '^(VERSION_CODE|VERSION_NAME)=' android/gradle.properties

    - name: Inspect gradle.properties (show invisibles)
      shell: bash
      run: |
        echo "----- android/gradle.properties with end-of-line markers -----"
        sed -n 'l' android/gradle.properties
        echo "----- Grep the critical lines -----"
        grep -n '^\(NDK_VERSION\|COMPILE_SDK\|BUILD_TOOLS\|VERSION_CODE\|VERSION_NAME\)=' android/gradle.properties || true

    - name: Dump env (filtered)
      shell: bash
      run: |
        env | egrep '(^|_)NDK|VERSION_CODE|VERSION_NAME|COMPILE_SDK|BUILD_TOOLS' || true

    - name: Sanitize android/gradle.properties (newline & stray merges)
      shell: bash
      run: |
        set -euo pipefail
        FILE=android/gradle.properties

        # Ensure file ends with a newline to prevent line merges
        tail -c1 "$FILE" | od -An -t x1 | grep -q '0a' || echo >> "$FILE"

        # Clean BUILD_TOOLS (keep leading digits and dots only)
        if grep -qE '^BUILD_TOOLS=' "$FILE"; then
          BT=$(grep -E '^BUILD_TOOLS=' "$FILE" | cut -d= -f2-)
          BT_CLEAN=$(printf "%s" "$BT" | sed 's/^\([0-9][0-9.]*\).*/\1/')
          sed -i.bak -E "s|^BUILD_TOOLS=.*$|BUILD_TOOLS=${BT_CLEAN}|" "$FILE"
        fi

        # Clean NDK_VERSION (same idea)
        if grep -qE '^NDK_VERSION=' "$FILE"; then
          NDK=$(grep -E '^NDK_VERSION=' "$FILE" | cut -d= -f2-)
          NDK_CLEAN=$(printf "%s" "$NDK" | sed 's/^\([0-9][0-9.]*\).*/\1/')
          sed -i.bak -E "s|^NDK_VERSION=.*$|NDK_VERSION=${NDK_CLEAN}|" "$FILE"
        fi

        # (Optional) ensure pure numbers for these
        for K in COMPILE_SDK TARGET_SDK MIN_SDK; do
          if grep -qE "^$K=" "$FILE"; then
            V=$(grep -E "^$K=" "$FILE" | cut -d= -f2-)
            V_CLEAN=$(printf "%s" "$V" | sed 's/^\([0-9][0-9]*\).*/\1/')
            sed -i.bak -E "s|^$K=.*$|$K=${V_CLEAN}|" "$FILE"
          fi
        done

        # Remove VERSION_* so they ONLY come from -P / env
        sed -i -E '/^(VERSION_CODE|VERSION_NAME)=/d' "$FILE"

        echo "gradle.properties (tail, with $ markers):"
        tail -n 20 "$FILE" | sed -n 'l'

    - name: Generate RN Codegen (app + any modules)
      shell: bash
      run: |
        set -e
        cd android
        TASKS=":app:generateCodegenArtifactsFromSchema"
        PROJECTS=$(./gradlew -q projects | awk -F"'" '/Project/{print $2}' | grep '^:' || true)
        for P in $PROJECTS; do
          MOD_TASKS=$(./gradlew -q ${P}:tasks --all | awk '/^generateCodegenArtifactsFromSchema[[:alnum:]]*/{print $1}' || true)
          for T in $MOD_TASKS; do TASKS="$TASKS ${P}:$T"; done
        done
        ./gradlew -PnewArchEnabled=true \
          -PVERSION_CODE='${{ inputs.build-number }}' \
          -PVERSION_NAME='${{ inputs.version }}' \
          $TASKS --no-daemon --stacktrace
      env:
        VERSION_CODE: ${{ inputs.build-number }}
        VERSION_NAME: ${{ inputs.version }}

    - name: Build Android Release AAB
      shell: bash
      run: |
        cd android
        ./gradlew -PnewArchEnabled=true \
          -PVERSION_CODE='${{ inputs.build-number }}' \
          -PVERSION_NAME='${{ inputs.version }}' \
          bundleRelease --no-daemon --stacktrace --info
      env:
        VERSION_CODE: ${{ inputs.build-number }}
        VERSION_NAME: ${{ inputs.version }}
        KEYSTORE_PASSWORD: ${{ inputs.keystore-password }}
        KEYSTORE_ALIAS: ${{ inputs.keystore-alias }}
        KEY_PASSWORD: ${{ inputs.key-password }}

    - name: Upload Release AAB artifact
      uses: actions/upload-artifact@v4
      with:
        name: app-release-bundle
        path: android/app/build/outputs/bundle/release/app-release.aab
        retention-days: 7
