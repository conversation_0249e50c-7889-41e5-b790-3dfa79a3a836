import React, { useCallback, useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components';
import { fetchJobDetailAPI } from '@/networks/jobs';
import type { JobCandidateFetchOneResultI } from '@/networks/jobs/types';

export type JobDetailPropsI = {
  jobId: string;
};

const JobDetail: React.FC<JobDetailPropsI> = ({ jobId }) => {
  const router = useRouter();
  const [job, setJob] = useState<JobCandidateFetchOneResultI | null>(null);
  const [loading, setLoading] = useState(false);
  const [applying, setApplying] = useState(false);

  const load = useCallback(async () => {
    try {
      setLoading(true);
      const res = await fetchJobDetailAPI(jobId);
      setJob(res);
    } catch (e) {
      console.error('Failed to load job', e);
    } finally {
      setLoading(false);
    }
  }, [jobId]);

  useEffect(() => {
    load();
  }, [load]);

  const onApplyJob = async () => {
    try {
      if (!job?.id) return;
      setApplying(true);
      // TODO: Implement job application API call
      console.log('Applying to job:', job.id);
      // await applyToJobAPI(job.id);
      await load(); // Reload to get updated application status
    } catch (e) {
      console.error('Failed to apply to job', e);
    } finally {
      setApplying(false);
    }
  };

  const title = job?.designation?.name || 'Job';
  const companyName = job?.entity?.name || '—';
  const createdAt = job?.createdAt
    ? new Date(job.createdAt).toLocaleDateString()
    : '';
  const matchingPercentage = job?.matching || 0;
  const applicationStatus = job?.applicationStatus;
  const hasApplied =
    applicationStatus === 'PENDING' ||
    applicationStatus === 'SHORTLISTED' ||
    applicationStatus === 'OFFERED' ||
    applicationStatus === 'ACCEPTED_BY_APPLICANT';

  return (
    <section className="lg:col-span-6 col-span-1" data-job-id={jobId}>
      <div className="bg-white border border-gray-200 rounded-2xl shadow-sm">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-start justify-between gap-4">
            <div>
              <div className="flex items-center text-gray-600 text-sm">
                <span className="inline-block h-4 w-4 rounded-full bg-gray-200 mr-2" />
                {companyName}
              </div>
              <h1 className="mt-1 text-2xl font-semibold text-gray-900">
                {title}
              </h1>
              <div className="mt-1 flex items-center gap-4 text-sm">
                <span className="text-gray-600">{createdAt}</span>
                {matchingPercentage > 0 && (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    {matchingPercentage}% match
                  </span>
                )}
                {applicationStatus && (
                  <span
                    className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      applicationStatus === 'PENDING'
                        ? 'bg-yellow-100 text-yellow-800'
                        : applicationStatus === 'SHORTLISTED'
                          ? 'bg-blue-100 text-blue-800'
                          : applicationStatus === 'OFFERED'
                            ? 'bg-purple-100 text-purple-800'
                            : applicationStatus === 'ACCEPTED_BY_APPLICANT'
                              ? 'bg-green-100 text-green-800'
                              : applicationStatus === 'REJECTED_BY_ENTITY'
                                ? 'bg-red-100 text-red-800'
                                : 'bg-gray-100 text-gray-800'
                    }`}
                  >
                    {applicationStatus === 'PENDING'
                      ? 'Applied'
                      : applicationStatus === 'SHORTLISTED'
                        ? 'Shortlisted'
                        : applicationStatus === 'OFFERED'
                          ? 'Offered'
                          : applicationStatus === 'ACCEPTED_BY_APPLICANT'
                            ? 'Accepted'
                            : applicationStatus === 'REJECTED_BY_ENTITY'
                              ? 'Rejected'
                              : applicationStatus}
                  </span>
                )}
              </div>
            </div>
            <button
              type="button"
              aria-label="More options"
              className="text-gray-400 hover:text-gray-600"
            >
              ···
            </button>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-5">
            <Button
              variant={hasApplied ? 'outline' : 'default'}
              className="w-full h-10"
              onClick={onApplyJob}
              disabled={!job || applying || hasApplied}
            >
              {applying ? 'Applying…' : hasApplied ? 'Applied' : 'Apply Now'}
            </Button>
            <Button
              variant="outline"
              className="w-full h-10"
              onClick={() => router.back()}
            >
              Back to Jobs
            </Button>
          </div>
        </div>

        <div className="p-6 space-y-8">
          {loading ? (
            <div className="text-center text-gray-500">Loading…</div>
          ) : (
            <>
              <section>
                <h3 className="text-base font-semibold text-gray-900 mb-2">
                  About
                </h3>
                <p className="text-[15px] leading-6 text-gray-700">
                  {/* Placeholder for description until backend exposes details */}
                  The organisation is hiring for {title}.
                </p>
              </section>

              <section>
                <h3 className="text-base font-semibold text-gray-900 mb-2">
                  Roles and responsibilities
                </h3>
                <ul className="list-disc pl-5 text-[15px] leading-6 text-gray-700 space-y-1">
                  <li>
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit.
                  </li>
                  <li>Pellentesque scelerisque diam eget fringilla tempor</li>
                  <li>Sed facilisis porttitor purus venenatis ultrices</li>
                  <li>Donec nunc quam, vestibulum eget faucibus sit amet,</li>
                </ul>
              </section>

              <section>
                <h3 className="text-base font-semibold text-gray-900 mb-2">
                  Requirements
                </h3>
                <ul className="list-disc pl-5 text-[15px] leading-6 text-gray-700 space-y-1">
                  <li>Bachelor's degree in relevant field</li>
                  <li>2+ years of experience in similar role</li>
                  <li>Strong communication skills</li>
                  <li>Ability to work in a team environment</li>
                </ul>
              </section>
            </>
          )}
        </div>
      </div>
    </section>
  );
};

export default JobDetail;
